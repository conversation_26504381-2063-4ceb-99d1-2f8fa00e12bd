<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="nav-group" @mouseenter="stopAutoplay" @mouseleave="resumeAutoplay">
        <el-button type="primary" :icon="Back" class="nav-btn prev-btn" @click="handlePrev">
          <div class="btn-bg"></div>
        </el-button>
        <div class="chart-title" @click="handleChartClick">
          {{ params[currentIndex].title }}
        </div>
        <el-button type="primary" :icon="Right" class="nav-btn next-btn" @click="handleNext">
          <div class="btn-bg"></div>
        </el-button>
      </div>
    </div>
    <div class="chart-content" ref="contentRef" @mouseenter="stopAutoplay" @mouseleave="resumeAutoplay">
      <Transition name="fade" mode="out-in">
        <div class="main-flex" :key="currentIndex">
          <div class="main-item" v-for="(item, k) in params[currentIndex].data" :key="k">
            <div class="cardBox-wrapper">
              <div class="cardBox">
                <div class="item">
                  <div class="icon" :class="'icon' + k">
                    <IconifyIcon class="iconfont" :icon="item.icon" @click="handleChartClick"></IconifyIcon>
                    <!-- <i class="iconfont" :class="item.icon" @click="handleChartClick"></i> -->
                  </div>
                  <div class="text">
                    <div class="dataName">{{ item.name }}</div>
                    <div class="dataValue mt8" :style="{ color: item.color[0] }">
                      {{ item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import { Right, Back } from "@element-plus/icons-vue";

// 定义数据类型
interface CardItem {
  name: string;
  value: string;
  icon: string;
  color: string[];
}

interface ParamsItem {
  title: string;
  data: CardItem[];
}

// 定义数据
const params: ParamsItem[] = [
  {
    title: "当日开学校区网络情况",
    data: [
      {
        name: "保障时段",
        value: "8:00-12:00",
        icon: "mdi:clock-time-eight-outline", // 时钟图标表示时段
        color: ["#14B4FF"]
      },
      {
        name: "今日新生开学校区",
        value: "25个",
        icon: "mdi:school-outline", // 学校建筑图标
        color: ["#00FF92"]
      },
      {
        name: "5G分流比",
        value: "74.2%",
        icon: "mdi:signal-5g", // 5G信号图标
        color: ["#FFE125"]
      },
      {
        name: "高负荷小区",
        value: "3个",
        icon: "mdi:alert-octagon-outline", // 警告图标表示高负荷
        color: ["#A4D6E8"]
      }
    ]
  },
  {
    title: "已开学校区网络概况",
    data: [
      {
        name: "新生开学",
        value: "92所",
        icon: "mdi:account-group-outline", // 人群图标表示新生
        color: ["#14B4FF"]
      },
      {
        name: "老生开学",
        value: "104所",
        icon: "mdi:account-multiple-outline", // 多人图标表示老生
        color: ["#00FF92"]
      },
      {
        name: "5G分流比",
        value: "72.3%",
        icon: "mdi:chart-donut", // 环形图表示比例
        color: ["#FFE125"]
      },
      {
        name: "云瞰移动用户占比",
        value: "48.5%",
        icon: "mdi:cloud-outline", // 云图标
        color: ["#A4D6E8"]
      }
    ]
  }
];

const currentIndex = ref(0);
const contentRef = ref<HTMLElement | null>(null);
let autoplayTimer: number | null = null;
const isUserInteracting = ref(false);

// 切换方法
const handlePrev = () => {
  stopAutoplay();
  changeIndex(-1);
};

const handleNext = () => {
  stopAutoplay();
  changeIndex(1);
};

const changeIndex = (step: number) => {
  const newIndex = (currentIndex.value + step + params.length) % params.length;
  currentIndex.value = newIndex;
};

// 自动播放控制
const startAutoplay = () => {
  if (isUserInteracting.value) return;

  if (autoplayTimer) {
    clearInterval(autoplayTimer);
  }

  autoplayTimer = window.setInterval(() => {
    if (!document.hidden && !isUserInteracting.value) {
      changeIndex(1);
    }
  }, 4000);
};

const stopAutoplay = () => {
  isUserInteracting.value = true;
  if (autoplayTimer) {
    clearInterval(autoplayTimer);
    autoplayTimer = null;
  }
};

const resumeAutoplay = () => {
  isUserInteracting.value = false;
  startAutoplay();
};

// 点击事件
const handleChartClick = () => {
  stopAutoplay();
  emit("clickEvent", params[currentIndex.value].title, "");
};

// 页面可见性处理函数
const handleVisibilityChange = () => {
  if (document.hidden) {
    stopAutoplay();
  } else {
    resumeAutoplay();
  }
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener("visibilitychange", handleVisibilityChange);

  nextTick(() => {
    setTimeout(() => {
      startAutoplay();
    }, 300);
  });
});

onBeforeUnmount(() => {
  stopAutoplay();
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});

const emit = defineEmits<{
  (e: "clickEvent", title: string, flag?: string): void;
}>();
</script>

<style scoped lang="scss">
.chart-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .chart-header {
    position: relative;
    display: flex;
    flex: none;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 0;
    margin-bottom: 10px;
    .nav-group {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 4px;
      background: linear-gradient(
        90deg,
        rgb(18 142 255 / 0%) 0%,
        rgb(18 142 255 / 10%) 25%,
        rgb(18 142 255 / 10%) 75%,
        rgb(18 142 255 / 0%) 100%
      );
      backdrop-filter: blur(4px);
      border-radius: 20px;
      .chart-title {
        position: relative;
        min-width: 200px;
        padding: 0 15px;
        font-size: 20px;
        font-weight: 500;
        color: #b1d8ff;
        text-align: center;
        cursor: pointer;
        transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        &::after {
          position: absolute;
          bottom: -2px;
          left: 50%;
          width: 0;
          height: 2px;
          content: "";
          background: linear-gradient(90deg, transparent, #00d5ff, transparent);
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateX(-50%);
        }
        &:hover {
          color: #00d5ff;
          text-shadow: 0 0 15px rgb(0 213 255 / 40%);
          &::after {
            width: 80%;
            opacity: 1;
          }
        }
      }
      .nav-btn {
        position: absolute;
        top: 50%;
        width: 28px;
        height: 28px;
        padding: 0;
        overflow: hidden;
        color: #b1d8ff;
        background: transparent;
        border: 1px solid rgb(18 142 255 / 30%);
        border-radius: 6px;
        transition: all 0.3s ease;
        transform: translateY(-50%);
        &.prev-btn {
          left: 0;
        }
        &.next-btn {
          right: 0;
        }
        .btn-bg {
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgb(18 142 255 / 10%) 0%, rgb(18 142 255 / 20%) 50%, rgb(18 142 255 / 10%) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        &:hover {
          color: #00d5ff;
          border-color: rgb(18 142 255 / 60%);
          box-shadow: 0 2px 8px rgb(0 213 255 / 20%);
          transform: translateY(calc(-50% - 1px));
          .btn-bg {
            opacity: 1;
          }
        }
        &:active {
          transform: translateY(-50%);
        }
        :deep(.el-icon) {
          z-index: 1;
          font-size: 16px;
        }
      }
    }
  }
  .chart-content {
    position: relative;
    flex: 1;
    min-height: 0;
    padding: 10px;
    overflow: hidden;
    backdrop-filter: blur(4px);
    border: 1px solid rgb(18 142 255 / 10%);
    border-radius: 8px;
    .main-flex {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      height: 100%;
      .main-item {
        box-sizing: border-box;
        flex: 1 1 calc(50% - 5px);
        border: 0 solid #dddddd;
        .cardBox-wrapper {
          position: relative;
          height: 100%;
          padding: 2px;
          margin: -2px;
        }
        .cardBox {
          position: relative;
          height: 100%;
          text-align: center;
          cursor: pointer;
          background-image: radial-gradient(
            circle at 50% 0%,
            rgb(23 173 255 / 20%) 0%,
            rgb(23 173 255 / 20%) 0%,
            rgb(40 115 255 / 6%) 99%
          );
          border-radius: 4px;
          box-shadow: 0 0 0 transparent;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateY(0);
          &::before {
            position: absolute;
            inset: 0;
            padding: 1px;
            content: "";
            background: linear-gradient(135deg, transparent, transparent, rgb(18 142 255 / 30%));
            border-radius: 4px;
            opacity: 0;
            mask-image: linear-gradient(#ffffff 0 0), linear-gradient(#ffffff 0 0);
            mask-composite: exclude;
            transition: opacity 0.3s ease;
          }
          &:hover {
            background-image: radial-gradient(
              circle at 50% 0%,
              rgb(23 173 255 / 30%) 0%,
              rgb(23 173 255 / 25%) 0%,
              rgb(40 115 255 / 10%) 99%
            );
            box-shadow: 0 4px 12px rgb(18 142 255 / 15%);
            transform: translateY(-2px);
            &::before {
              opacity: 1;
            }
            .item {
              .icon {
                transform: translateZ(0) scale(1.02);
                .iconfont {
                  filter: brightness(1.2);
                  transform: translateZ(0) scale(1.05);
                }
              }
              .text {
                .dataName {
                  color: #ffffff;
                  text-shadow: 0 0 8px rgb(255 255 255 / 30%);
                }
                .dataValue {
                  filter: brightness(1.1);
                  transform: scale(1.05);
                }
              }
            }
          }
          .item {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            .icon {
              display: inline-block;
              width: 87px;
              height: 88px;
              line-height: 88px;
              text-align: center;
              background: url("@/views/dataScreen/assets/images/digitalHumanIconBg.png") no-repeat;
              transition: transform 0.3s ease;
              transform: translateZ(0);
              will-change: transform;
              .iconfont {
                position: relative;

                // top: -7px;
                left: 6px;
                align-items: center;
                font-size: 28px;
                transition: all 0.3s ease;
                transform: translateZ(0);
              }
              &.icon0 .iconfont {
                color: #00d5ff;
                text-shadow: 0 0 32px #00d5ff;
                background: linear-gradient(to bottom, #a2e3ff, #0db6ff);
                background-clip: text;
              }
              &.icon1 .iconfont {
                color: #ffb900;
                text-shadow: 0 0 32px #ffb900;
                background: linear-gradient(to bottom, #fff1cd, #ecc14f);
                background-clip: text;
              }
              &.icon2 .iconfont {
                color: #40e792;
                text-shadow: 0 0 32px #40e792;
                background: linear-gradient(to bottom, #c5ffe2, #40e792);
                background-clip: text;
              }
              &.icon3 .iconfont {
                color: #a4d6e8;
                text-shadow: 0 0 32px #a4d6e8;
                background: linear-gradient(to bottom, #d9dee0, #a4d6e8);
                background-clip: text;
              }
            }
            .text {
              width: calc(100% - 87px);
              text-align: center;
              .dataName {
                font-size: 14px;
                color: #ffffff;
                transition: all 0.3s ease;
              }
              .dataValue {
                font-family: futura;
                font-size: 18px;
                color: #00d5ff;
                transition: all 0.3s ease;
                transform-origin: center;
              }
            }
          }
        }
      }
    }
  }
}
</style>
