<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="nav-group" @mouseenter="stopAutoplay" @mouseleave="startAutoplay">
        <el-button type="primary" :icon="Back" class="nav-btn prev-btn" @click="handlePrev">
          <div class="btn-bg"></div>
        </el-button>
        <div class="chart-title">{{ currentParams.title }}</div>
        <el-button type="primary" :icon="Right" class="nav-btn next-btn" @click="handleNext">
          <div class="btn-bg"></div>
        </el-button>
      </div>
    </div>
    <div class="chart-content" ref="contentRef" @mouseenter="stopAutoplay" @mouseleave="startAutoplay">
      <Transition name="fade" mode="out-in">
        <div class="content-wrapper" :key="currentIndex">
          <div class="data-grid">
            <div
              class="data-box hasHover"
              v-for="(item, k) in currentPageData"
              :key="item.flag || k"
              @click="handleClick(item.name, item.flag)"
            >
              <div class="box-content">
                <el-tooltip
                  class="box-item"
                  effect="customized"
                  :content="item.name"
                  placement="top-start"
                  :visible-arrow="true"
                  transition="el-fade-in-linear"
                  popper-class="data-tooltip"
                >
                  <div class="data-name">{{ item.name }}</div>
                </el-tooltip>
                <div class="data-value-wrapper">
                  <span class="data-value">{{ item.value }}</span>
                  <i :class="getArrowClass(item.status)" class="status-icon"></i>
                </div>
              </div>
              <div class="corner-decoration top-left"></div>
              <div class="corner-decoration top-right"></div>
              <div class="corner-decoration bottom-left"></div>
              <div class="corner-decoration bottom-right"></div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from "vue";
import { Right, Back } from "@element-plus/icons-vue";

/**
 * @typedef {Object} DataItem 数据项类型
 * @property {string} name - 指标名称
 * @property {number} value - 指标数值
 * @property {'up'|'down'} status - 指标状态
 * @property {string} [flag] - 可选标识符
 */
interface DataItem {
  name: string;
  value: number;
  status: "up" | "down";
  flag?: string;
}

/**
 * @typedef {Object} ParamsItem 页面参数类型
 * @property {string} title - 页面标题
 * @property {DataItem[]} data - 页面数据列表
 */
interface ParamsItem {
  title: string;
  data: DataItem[];
}

// 数据源
const params: ParamsItem[] = [
  {
    title: "网络运行情况",
    data: [
      { name: "网络质量", value: 95, status: "up", flag: "network" },
      { name: "用户满意度", value: 88, status: "up", flag: "satisfaction" },
      { name: "故障处理", value: 92, status: "down", flag: "fault" },
      { name: "网络覆盖", value: 85, status: "up", flag: "coverage" },
      { name: "信号强度", value: 90, status: "up", flag: "signal" },
      { name: "响应时间", value: 78, status: "down", flag: "response" },
      { name: "带宽利用", value: 82, status: "up", flag: "bandwidth" },
      { name: "连接稳定性", value: 94, status: "up", flag: "stability" },
      { name: "数据传输", value: 87, status: "down", flag: "transfer" }
    ]
  },
  {
    title: "设备运行状态",
    data: [
      { name: "设备在线率", value: 98, status: "up", flag: "online" },
      { name: "设备故障率", value: 3, status: "down", flag: "error" },
      { name: "设备利用率", value: 86, status: "up", flag: "usage" },
      { name: "维护响应", value: 92, status: "up", flag: "maintenance" },
      { name: "设备温度", value: 75, status: "down", flag: "temperature" },
      { name: "运行时长", value: 95, status: "up", flag: "runtime" },
      { name: "性能指标", value: 88, status: "up", flag: "performance" },
      { name: "能耗水平", value: 82, status: "down", flag: "energy" },
      { name: "更新状态", value: 90, status: "up", flag: "update" }
    ]
  }
];

// 状态管理
const currentIndex = ref(0);
const contentRef = ref<HTMLElement | null>(null);
const autoplayTimer = ref<number | null>(null);
const isUserInteracting = ref(false);
const AUTOPLAY_INTERVAL = 4000; // 自动播放间隔
const ITEMS_PER_PAGE = 9; // 每页显示数量

/**
 * @description 获取当前页面参数
 */
const currentParams = computed(() => params[currentIndex.value]);

/**
 * @description 获取当前页面数据
 */
const currentPageData = computed(() => {
  return currentParams.value.data.slice(0, ITEMS_PER_PAGE);
});

/**
 * @description 获取箭头样式类名
 * @param {string} status - 状态值
 * @returns {string} - 样式类名
 */
const getArrowClass = (status: "up" | "down"): string => (status === "up" ? "arrowUp" : "arrowDown");

/**
 * @description 切换到上一页
 */
const handlePrev = () => {
  stopAutoplay();
  changeIndex(-1);
};

/**
 * @description 切换到下一页
 */
const handleNext = () => {
  stopAutoplay();
  changeIndex(1);
};

/**
 * @description 更改当前索引
 * @param {number} step - 步进值
 */
const changeIndex = (step: number): void => {
  currentIndex.value = (currentIndex.value + step + params.length) % params.length;
};

/**
 * @description 开始自动播放
 */
const startAutoplay = (): void => {
  if (isUserInteracting.value) return;

  stopAutoplay();

  autoplayTimer.value = window.setInterval(() => {
    if (!document.hidden && !isUserInteracting.value) {
      changeIndex(1);
    }
  }, AUTOPLAY_INTERVAL);
};

/**
 * @description 停止自动播放
 */
const stopAutoplay = (): void => {
  isUserInteracting.value = true;

  if (autoplayTimer.value) {
    clearInterval(autoplayTimer.value);
    autoplayTimer.value = null;
  }
};

/**
 * @description 处理指标点击事件
 * @param {string} name - 指标名称
 * @param {string} [flag] - 指标标识
 */
const handleClick = (name: string, flag?: string): void => {
  stopAutoplay();
  emit("clickEvent", currentParams.value.title, flag);
};

/**
 * @description 处理页面可见性变化
 */
const handleVisibilityChange = (): void => {
  if (document.hidden) {
    stopAutoplay();
  } else {
    isUserInteracting.value = false;
    startAutoplay();
  }
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener("visibilitychange", handleVisibilityChange);

  nextTick(() => {
    setTimeout(startAutoplay, 300);
  });
});

onBeforeUnmount(() => {
  stopAutoplay();
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});

// 事件定义
const emit = defineEmits<{
  (e: "clickEvent", title: string, flag?: string): void;
}>();
</script>

<style scoped lang="scss">
.chart-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 5px;
  border-radius: 12px;
  .chart-header {
    position: relative;
    display: flex;
    flex: none;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 0;
    margin-bottom: 10px;
    .nav-group {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 4px;
      background: linear-gradient(
        90deg,
        rgb(18 142 255 / 0%) 0%,
        rgb(18 142 255 / 10%) 25%,
        rgb(18 142 255 / 10%) 75%,
        rgb(18 142 255 / 0%) 100%
      );
      backdrop-filter: blur(4px);
      border-radius: 20px;
      .chart-title {
        position: relative;
        min-width: 200px;
        padding: 0 15px;
        font-size: 20px;
        font-weight: 500;
        color: #b1d8ff;
        text-align: center;
        cursor: pointer;
        transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        &::after {
          position: absolute;
          bottom: -2px;
          left: 50%;
          width: 0;
          height: 2px;
          content: "";
          background: linear-gradient(90deg, transparent, #00d5ff, transparent);
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateX(-50%);
        }
        &:hover {
          color: #00d5ff;
          text-shadow: 0 0 15px rgb(0 213 255 / 40%);
          &::after {
            width: 80%;
            opacity: 1;
          }
        }
      }
      .nav-btn {
        position: absolute;
        top: 50%;
        width: 28px;
        height: 28px;
        padding: 0;
        overflow: hidden;
        color: #b1d8ff;
        background: transparent;
        border: 1px solid rgb(18 142 255 / 30%);
        border-radius: 6px;
        transition: all 0.3s ease;
        transform: translateY(-50%);
        &.prev-btn {
          left: 0;
        }
        &.next-btn {
          right: 0;
        }
        .btn-bg {
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgb(18 142 255 / 10%) 0%, rgb(18 142 255 / 20%) 50%, rgb(18 142 255 / 10%) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        &:hover {
          color: #00d5ff;
          border-color: rgb(18 142 255 / 60%);
          box-shadow: 0 2px 8px rgb(0 213 255 / 20%);
          transform: translateY(calc(-50% - 1px));
          .btn-bg {
            opacity: 1;
          }
        }
        &:active {
          transform: translateY(-50%);
        }
        :deep(.el-icon) {
          z-index: 1;
          font-size: 16px;
        }
      }
    }
  }
  .chart-content {
    position: relative;
    flex: 1;
    min-height: 0;
    padding: 15px;
    backdrop-filter: blur(4px);
    border-radius: 12px;
    .content-wrapper {
      height: 100%;
    }
    .data-grid {
      display: grid;
      grid-template-rows: repeat(3, 1fr);
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      width: 100%;
      height: 100%;
    }
    .data-box {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      background-image: radial-gradient(
        circle at 50% 0%,
        rgb(23 173 255 / 20%) 0%,
        rgb(23 173 255 / 20%) 0%,
        rgb(40 115 255 / 6%) 99%
      );
      border-radius: 3px;
      box-shadow: 0 4px 20px rgb(0 213 255 / 0%);
      transition: all 0.3s ease;
      transform: translateY(0);
      will-change: transform, box-shadow;
      &::before {
        position: absolute;
        inset: 0;
        content: "";
        background: linear-gradient(135deg, rgb(23 173 255 / 10%) 0%, rgb(23 173 255 / 5%) 100%);
        opacity: 0;
        transition: opacity 0.3s;
      }
      .box-content {
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 8px;
        text-align: center;
        .box-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
      .data-name {
        display: -webkit-box;
        width: 100%;
        margin-bottom: 6px;
        overflow: hidden;
        font-size: 14px;
        line-height: 1.2;
        color: #b1d8ff;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        word-break: break-all;
        -webkit-box-orient: vertical;
      }
      .data-value-wrapper {
        display: flex;
        gap: 4px;
        align-items: center;
        justify-content: center;
      }
      .data-value {
        font-family: Orbitron, sans-serif;
        font-size: 18px;
        font-weight: 500;
        color: #00d5ff;
        text-shadow: 0 0 10px rgb(0 213 255 / 30%);
      }
      .status-icon {
        width: 14px;
        height: 14px;
      }
      &:hover {
        z-index: 1;
        box-shadow: 0 4px 20px rgb(0 213 255 / 15%);
        transform: translateY(-2px);
        &::before {
          opacity: 1;
        }
        .data-name {
          color: #ffffff;
        }
        .data-value {
          text-shadow: 0 0 15px rgb(0 213 255 / 50%);
        }
      }
    }
  }
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 多行文本省略
.multiline-ellipsis {
  display: -webkit-box;
  max-height: 2em;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  word-break: break-all;
  -webkit-box-orient: vertical;
}
</style>
