<?xml version="1.0" encoding="UTF-8"?>
<svg width="42px" height="32px" viewBox="0 0 42 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Point</title>
    <defs>
        <radialGradient cx="50%" cy="89.4772312%" fx="50%" fy="89.4772312%" r="81.7437694%" gradientTransform="translate(0.5, 0.8948), scale(0.5714, 1), rotate(90), scale(1, 1.5995), translate(-0.5, -0.8948)" id="radialGradient-1">
            <stop stop-color="#31FFE1" offset="0%"></stop>
            <stop stop-color="#31FFE1" stop-opacity="0" offset="100%"></stop>
        </radialGradient>
        <ellipse id="path-2" cx="14" cy="8" rx="14" ry="8"></ellipse>
        <filter x="-37.5%" y="-65.6%" width="175.0%" height="231.2%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.192156863   0 0 0 0 1   0 0 0 0 0.882352941  0 0 0 0.16 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-44.6%" y="-78.1%" width="189.3%" height="256.2%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="3.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-4" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.192156863   0 0 0 0 1   0 0 0 0 0.882352941  0 0 0 0.4 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <radialGradient cx="50%" cy="100%" fx="50%" fy="100%" r="84.6027938%" gradientTransform="translate(0.5, 1), scale(0.5805, 1), rotate(90), scale(1, 2.1741), translate(-0.5, -1)" id="radialGradient-5">
            <stop stop-color="#BEFFFB" offset="0%"></stop>
            <stop stop-color="#31FFE1" stop-opacity="0.08" offset="100%"></stop>
        </radialGradient>
        <ellipse id="path-6" cx="14" cy="7.11111111" rx="6.125" ry="3.55555556"></ellipse>
        <filter x="-85.7%" y="-147.7%" width="271.4%" height="395.3%" filterUnits="objectBoundingBox" id="filter-7">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.192156863   0 0 0 0 1   0 0 0 0 0.882352941  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-93.9%" y="-161.7%" width="287.8%" height="423.4%" filterUnits="objectBoundingBox" id="filter-8">
            <feGaussianBlur stdDeviation="2.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="-4" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.192156863   0 0 0 0 1   0 0 0 0 0.882352941  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="100%" x2="50%" y2="3.061617e-15%" id="linearGradient-9">
            <stop stop-color="#66E2FB" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#66E2FB" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="首页" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="首页4.0" transform="translate(-824, -174)">
            <g id="ditu" transform="translate(481, 114)">
                <g id="编组-38" transform="translate(34, 6)">
                    <g id="编组-20" transform="translate(131, 0)">
                        <g id="编组-36" transform="translate(77, 27)">
                            <g id="ZJ-City" transform="translate(0, 27)">
                                <g id="Dot" transform="translate(90, 0)">
                                    <g id="Point" transform="translate(18, 0)">
                                        <g id="Dot-Line" transform="translate(0, 9)">
                                            <g id="Line">
                                                <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                                                <use fill-opacity="0.08" fill="#31FFE1" fill-rule="evenodd" xlink:href="#path-2"></use>
                                                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-2"></use>
                                                <ellipse stroke="url(#radialGradient-1)" stroke-width="0.888888889" stroke-linejoin="square" cx="14" cy="8" rx="13.5555556" ry="7.55555556"></ellipse>
                                            </g>
                                            <g id="Line">
                                                <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                                                <use fill-opacity="0.32" fill="#31FFE1" fill-rule="evenodd" xlink:href="#path-6"></use>
                                                <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-6"></use>
                                                <ellipse stroke="url(#radialGradient-5)" stroke-width="0.888888889" stroke-linejoin="square" cx="14" cy="7.11111111" rx="5.68055556" ry="3.11111111"></ellipse>
                                            </g>
                                        </g>
                                        <path d="M4,0 L24,0 L24,16 C24,18.7614237 19.5228475,21 14,21 C8.4771525,21 4,18.7614237 4,16 L4,0 Z" id="Map-PointShape" fill="url(#linearGradient-9)"></path>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>