<template>
  <el-drawer v-model="drawerVisible" :destroy-on-close="true" size="450px" :title="`${drawerProps.title}`" class="custom-drawer">
    <el-form
      ref="ruleFormRef"
      label-width="100px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <div v-if="drawerProps.title != '工单督办'">
        <el-form-item label="外呼地址">
          <el-select v-model="drawerProps.row!.Url">
            <el-option v-for="item in ivrUrl" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="外呼号码" prop="Call">
          <el-input v-model="drawerProps.row!.Call" placeholder="外呼号码" clearable></el-input>
        </el-form-item>
        <el-form-item label="外呼话术" prop="Text">
          <el-input v-model="drawerProps.row!.Text" :rows="9" type="textarea" placeholder="外呼话术" clearable />
        </el-form-item>
        <el-form-item label="外呼信息">
          <el-input v-model.trim="Msg" :rows="9" type="textarea" />
        </el-form-item>
      </div>
      <div v-else>
        <el-form-item label="工单标题" prop="Title">
          <el-input v-model="drawerProps.row!.Title" placeholder="工单标题" clearable></el-input>
        </el-form-item>
        <el-form-item label="工单内容" prop="Content">
          <el-input v-model="drawerProps.row!.Content" :rows="9" type="textarea" placeholder="工单内容" clearable />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">取消</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">发起督办</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/modules/user";

/**
 * @typedef {Object} IvrUrlOption
 * @property {string} label - 显示标签
 * @property {string} value - 选项值
 */

/**
 * IVR外呼地址选项
 * @type {Array<IvrUrlOption>}
 */
const ivrUrl = [
  { label: "IVR外呼系统1", value: "http://ivr.example.com/api1" },
  { label: "IVR外呼系统2", value: "http://ivr.example.com/api2" }
];

/**
 * @typedef {Object} DrawerProps
 * @property {string} title - 抽屉标题
 * @property {boolean} isView - 是否仅查看模式
 * @property {Object} row - 表单数据
 * @property {Function} [api] - 提交API
 * @property {Function} [getTableList] - 获取表格数据的函数
 */

const userStore = useUserStore();
const Msg = ref("");

/**
 * 表单验证规则
 */
const rules = reactive({
  Call: [{ required: true, message: "请填写外呼号码" }],
  Text: [{ required: true, message: "请填写外呼话术" }],
  Url: [{ required: true, message: "请选择外呼地址" }],
  Title: [{ required: true, message: "请填写工单标题" }],
  Content: [{ required: true, message: "请填写工单内容" }],
  address: [{ required: true, message: "请填写居住地址" }]
});

/**
 * 抽屉属性类型定义
 */
interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<any>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

/**
 * 接收外部参数并打开抽屉
 * @param {DrawerProps} params - 抽屉参数
 */
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerProps.value.row.Title = "关于" + drawerProps.value.row.四级指标 + "指标的工作督办";
  drawerProps.value.row.Content =
    drawerProps.value.row.四级指标 +
    "指标本月当前情况为" +
    drawerProps.value.row["10月"] +
    "，未达到（基准值/挑战值），请针对该指标制定整改举措，设置完成期限，并按周使用阶段性回复提交整改进度直至达到挑战目标。";
  drawerProps.value.row.Url = ivrUrl[0].label;
  drawerProps.value.row.Call = "18374980013";
  drawerProps.value.row.Text =
    "【网络中台短板督办提醒】：收到一条来自" +
    "网络部" +
    userStore.userInfo.userName +
    "的关于(" +
    drawerProps.value.row.四级指标 +
    ")指标的工作督办，该项指标本月当前情况为" +
    drawerProps.value.row["10月"] +
    "，未达到（基准值/挑战值），您可以登陆网络看板查看相关信息，请您及时整改回复。";
  drawerVisible.value = true;
};

/**
 * 提交数据
 */
const ruleFormRef = ref<any>();
const handleSubmit = async () => {
  try {
    const res = await drawerProps.value.api!(drawerProps.value.row);
    Msg.value = JSON.stringify(JSON.parse(res.data), null, 4);
    ElMessage.success({ message: `${drawerProps.value.title} 成功！` });
  } catch (error) {
    console.log(error);
  }
};

defineExpose({
  acceptParams
});
</script>
