<template>
  <div class="dataScreen-container">
    <div class="dataScreen-content" ref="dataScreenRef">
      <LayoutHeader :title="title"></LayoutHeader>
      <div class="dataScreen-main">
        <slot></slot>
      </div>
      <LayoutFooter></LayoutFooter>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, withDefaults, defineProps } from "vue";
import LayoutHeader from "./LayoutHeader.vue";
import LayoutFooter from "./LayoutFooter.vue";

/**
 * @typedef {Object} Props
 * @property {string} [title] - 大屏标题
 * @property {number} [designWidth] - 设计宽度
 * @property {number} [designHeight] - 设计高度
 */

/**
 * @type {Props}
 */
interface Props {
  title?: string;
  designWidth?: number;
  designHeight?: number;
}

// 组件属性默认值
const props = withDefaults(defineProps<Props>(), {
  title: "网络中台",
  designWidth: 1920,
  designHeight: 1080
});

// 大屏容器引用
const dataScreenRef = ref<HTMLElement | null>(null);

/**
 * 初始化大屏容器尺寸和缩放
 */
onMounted(() => {
  if (dataScreenRef.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
    dataScreenRef.value.style.width = `${props.designWidth}px`;
    dataScreenRef.value.style.height = `${props.designHeight}px`;
  }
  window.addEventListener("resize", resize);
});

/**
 * 响应窗口大小变化，重新计算缩放比例
 */
const resize = () => {
  if (dataScreenRef.value) {
    dataScreenRef.value.style.transform = `scale(${getScale()}) translate(-50%, -50%)`;
  }
};

/**
 * 计算大屏缩放比例
 * @returns {number} 缩放比例
 */
const getScale = () => {
  const ww = window.innerWidth / props.designWidth;
  const wh = window.innerHeight / props.designHeight;
  return ww < wh ? ww : wh;
};

/**
 * 组件销毁前移除事件监听
 */
onBeforeUnmount(() => {
  window.removeEventListener("resize", resize);
});
</script>

<style lang="scss" scoped>
@import "../assets/style/index";
</style>
