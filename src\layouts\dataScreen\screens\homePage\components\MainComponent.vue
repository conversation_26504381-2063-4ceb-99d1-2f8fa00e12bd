<template>
  <div class="container">
    <!-- 条件渲染柱状图 -->
    <BarChart
      v-if="currentView === 'bar'"
      :title="network.title"
      :category="network.category"
      :bar-data="network.barData"
      :gird-bt="network.girdBt"
      :bar-width="network.barWidth"
      :max="max"
    />
    <!-- 条件渲染表格 -->
    <ProTable
      v-if="currentView === 'table'"
      class-name="no-card customTable"
      ref="proTable"
      :columns="columns"
      :data="tables"
      :pagination="false"
      :border="false"
      :tool-button="false"
      :cell-class-name="tableColClassName"
      @row-click="rowClick"
      :show-summary="true"
      :summary-method="getSummaries"
    >
    </ProTable>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive } from "vue";
// import BarChart from "@/components/ECharts/components/barChart.vue";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import type { TableColumnCtx } from "element-plus/es/components/table/src/table-column/defaults";

const emit = defineEmits(["clickEvent"]);
const currentView = ref<"bar" | "table">("bar"); // 控制当前视图，默认为柱状图
const proTable = ref<ProTableInstance>();
const max = 1200000;
const network = {
  title: "易访客户触达",
  category: ["易访用户数", "已触达数", "确认友好客户数", "低满待修复", "修复完成数", "修复成功数", "已短信关怀触达用户数"],
  barData: [[1167290, 407907, 164831, 9614, 8186, 5162, 855842]],
  girdBt: 120,
  barWidth: 20
};

const tables = [
  {
    部门: "网络部1",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部2",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部3",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部4",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部5",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部6",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部6",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部6",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  },
  {
    部门: "网络部6",
    受理量: "100",
    解决量: "100",
    解决率: "100",
    超时量: "100",
    低满量: "100"
  }
];
// 表格配置项
const columns = reactive<ColumnProps<any>[]>([
  { prop: "部门", label: "部门" },
  { prop: "受理量", label: "受理量" },
  { prop: "解决量", label: "解决量" },
  { prop: "解决率", label: "解决率" },
  { prop: "超时量", label: "超时量" },
  { prop: "低满量", label: "低满量" }
]);

// 标注背景颜色
const tableColClassName = ({ column, row }: { column: any; columnIndex: number; row: any; rowIndex: number }) => {
  if (column.label === "部门") return "white-text";
  if (column.label === "受理量" || column.label === "解决率") return "success-row";
  return "default-row";
  row;
};

// 单击行
const rowClick = (row: any, column: TableColumnCtx<any>) => {
  // console.log(row, column);
  emit("clickEvent", row.部门 + row.受理量 + row.解决量);
  column;
};

// 表尾合计行（自行根据条件计算）
interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx<T>[];
  data: T[];
}

// 表尾合计行
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  // console.log(columns, data);
  const sums: any[] = [];
  columns.forEach((column, index) => {
    if (index === 0) return (sums[index] = "合计");
    else {
      // values是每一列的数据是一个数组
      const values = data.map(item => Number(item[column.property]));

      // 数字列才进行合计计算
      if (!values.every(value => isNaN(value))) {
        // 对values进行一个累加操作，累加那些非NAN的值
        const total = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);

        // 年龄计算平均值
        if (column.property == "age") {
          sums[index] = total / data.length;
        }

        // 财产计算总数
        if (column.property == "money") {
          sums[index] = total;
        }
        sums[index] = <span class={"info-row"}>{total}</span>;
      }
    }
  });
  return sums;
};
</script>

<style scoped lang="scss">
.container {
  position: relative;

  // width: 1280px;
  height: 600px;
}
</style>
