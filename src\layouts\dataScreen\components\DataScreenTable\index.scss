// 添加发光动画效果
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgb(0 228 255 / 50%);
  }
  50% {
    box-shadow: 0 0 10px rgb(0 228 255 / 80%);
  }
  100% {
    box-shadow: 0 0 5px rgb(0 228 255 / 50%);
  }
}
.data-screen-table {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  color: #ffffff;

  // 表格外围轮廓效果
  &::before {
    position: absolute;
    inset: 0;
    pointer-events: none;
    content: "";
    border: 1px solid rgb(0 228 255 / 30%);
  }

  // 表格四角装饰 - 左上角
  &::after {
    position: absolute;
    top: 0;
    left: 0;
    width: 10px;
    height: 10px;
    content: "";
    border-top: 2px solid rgb(0 228 255 / 80%);
    border-left: 2px solid rgb(0 228 255 / 80%);
  }

  // 其他角装饰
  .corner-tr,
  .corner-bl,
  .corner-br {
    position: absolute;
    width: 10px;
    height: 10px;
  }
  .corner-tr {
    top: 0;
    right: 0;
    border-top: 2px solid rgb(0 228 255 / 80%);
    border-right: 2px solid rgb(0 228 255 / 80%);
  }
  .corner-bl {
    bottom: 0;
    left: 0;
    border-bottom: 2px solid rgb(0 228 255 / 80%);
    border-left: 2px solid rgb(0 228 255 / 80%);
  }
  .corner-br {
    right: 0;
    bottom: 0;
    border-right: 2px solid rgb(0 228 255 / 80%);
    border-bottom: 2px solid rgb(0 228 255 / 80%);
  }

  // 覆盖Element Plus表格样式
  :deep(.el-table) {
    --el-table-border-color: rgb(1 98 226 / 30%);
    --el-table-header-bg-color: rgb(1 19 67 / 50%);
    --el-table-tr-bg-color: transparent;
    --el-table-row-hover-bg-color: rgb(1 98 226 / 30%);
    --el-table-fixed-box-shadow: none;

    height: 100%; /* 确保表格高度占满容器 */
    font-size: 14px;
    color: #ffffff;
    background-color: transparent;

    // 去除表格边框
    &::before,
    &::after {
      display: none;
    }

    // 表头样式
    .el-table__header-wrapper {
      .el-table__header {
        border-color: rgb(1 98 226 / 50%);
      }
      th.el-table__cell {
        // 表头底部发光效果
        position: relative;
        font-weight: normal;
        color: #00e4ff;
        background-color: rgb(1 19 67 / 50%);
        border-bottom: 1px solid rgb(1 98 226 / 50%);
        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1px;
          content: "";
          background: linear-gradient(to right, transparent, rgb(0 228 255 / 80%), transparent);
        }
      }
    }

    // 表格主体样式
    .el-table__body-wrapper {
      height: calc(100% - 45px); /* 减去表头的高度 */
      overflow-y: auto; /* 确保内容滚动 */
      background-color: transparent;
      scroll-behavior: smooth; /* 平滑滚动 */
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }
      &::-webkit-scrollbar-thumb {
        background: rgb(0 228 255 / 50%);
        border-radius: 2px;
      }
      &::-webkit-scrollbar-track {
        background: rgb(1 19 67 / 30%);
        border-radius: 2px;
      }
      td.el-table__cell {
        height: 40px; /* 设置固定行高，便于滚动计算 */
        cursor: pointer !important;
        background-color: transparent;
        border-bottom: 1px solid rgb(1 98 226 / 30%);
        &.white-text {
          color: #ffffff;
        }
        &.success-row {
          color: #00e4ff;
        }
        &.warning-row {
          color: #ff9800;
        }
        &.default-row {
          color: #ffffff;
        }
        &:hover {
          text-decoration: underline !important;
        }
      }

      // 行交替颜色
      tr.even-row td {
        background-color: rgb(1 19 67 / 20%);
      }
      tr.odd-row td {
        background-color: transparent;
      }

      // 高亮当前行
      tr.current-row td {
        background-color: rgb(1 98 226 / 30%) !important;
      }
    }

    // 表尾样式
    .el-table__footer-wrapper {
      td.el-table__cell {
        // 合计行顶部发光效果
        position: relative;
        font-weight: bold;
        color: #ffcc00;
        background-color: rgb(1 19 67 / 50%);
        border-top: 1px solid rgb(1 98 226 / 50%);
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 1px;
          content: "";
          background: linear-gradient(to right, transparent, rgb(0 228 255 / 80%), transparent);
        }

        // 第一列"合计"文字颜色
        &:first-child {
          color: #00e4ff;
        }
      }
    }

    // 去除单元格边框
    .el-table__cell {
      border-right: none !important;
    }
  }
}
