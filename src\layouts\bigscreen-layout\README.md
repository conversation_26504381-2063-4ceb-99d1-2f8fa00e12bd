# 大屏布局主题系统

这个大屏布局主题系统提供了一个完整的解决方案，让大屏页面能够自动适应亮色和暗色主题，同时不会影响全局的对话框和组件样式。

## 特性

- 🎨 **主题自动切换**: 根据系统主题自动切换亮色/暗色模式
- 🔧 **独立样式系统**: 不会覆盖全局组件样式
- 🎯 **专用组件**: 提供大屏专用的对话框和设置组件
- 📱 **响应式设计**: 支持不同屏幕尺寸
- ⚡ **高性能**: 使用 CSS 变量和 Vue 3 响应式系统

## 目录结构

```
src/layouts/bigscreen-layout/
├── components/           # 大屏专用组件
│   ├── AiDialog/        # AI助手对话框
│   ├── Container/       # 容器组件
│   ├── Footer/          # 页脚组件
│   ├── Header/          # 头部组件
│   └── SettingsDialog/  # 设置对话框
├── theme/               # 主题配置
│   └── index.ts         # 主题系统核心文件
├── index.vue            # 大屏布局主组件
└── README.md            # 说明文档
```

## 使用方法

### 1. 在大屏页面中使用主题系统

```vue
<script setup lang="ts">
import { useBigscreenTheme } from '@/layouts/bigscreen-layout/theme/index';

const { bigscreenTheme, themeStore } = useBigscreenTheme();
</script>

<template>
  <div 
    class="my-bigscreen-page"
    :style="{ backgroundColor: bigscreenTheme.colors.mainBg }"
  >
    <!-- 使用主题颜色 -->
    <div 
      class="card"
      :style="{ 
        backgroundColor: bigscreenTheme.colors.cardBg,
        border: `1px solid ${bigscreenTheme.colors.borderColor}`,
        color: bigscreenTheme.colors.textColor
      }"
    >
      内容区域
    </div>
  </div>
</template>

<style scoped>
.my-bigscreen-page {
  background: v-bind('bigscreenTheme.gradients.background');
}

.card {
  box-shadow: v-bind('bigscreenTheme.shadows.card');
}
</style>
```

### 2. 使用大屏专用组件

```vue
<script setup lang="ts">
import { ref } from 'vue';
import AiDialog from '@/layouts/bigscreen-layout/components/AiDialog/index.vue';
import SettingsDialog from '@/layouts/bigscreen-layout/components/SettingsDialog/index.vue';

const aiDialogVisible = ref(false);
const settingsDialogVisible = ref(false);
</script>

<template>
  <!-- 触发按钮 -->
  <NButton @click="aiDialogVisible = true">打开AI助手</NButton>
  <NButton @click="settingsDialogVisible = true">打开设置</NButton>

  <!-- 对话框组件 -->
  <AiDialog v-model:visible="aiDialogVisible" />
  <SettingsDialog v-model:visible="settingsDialogVisible" />
</template>
```

## 主题配置

### 颜色配置

主题系统提供了完整的颜色配置：

```typescript
const bigscreenTheme = {
  colors: {
    // 基础颜色
    dialogBg: string,        // 对话框背景色
    borderColor: string,     // 边框颜色
    textColor: string,       // 主要文字颜色
    secondaryTextColor: string, // 次要文字颜色
    
    // 功能颜色
    chatBg: string,          // 聊天区域背景
    userMsgBg: string,       // 用户消息背景
    systemMsgBg: string,     // 系统消息背景
    inputBg: string,         // 输入框背景
    buttonBg: string,        // 按钮背景
    itemBg: string,          // 设置项背景
    
    // 布局颜色
    mainBg: string,          // 主背景色
    cardBg: string,          // 卡片背景色
    
    // 状态颜色
    successColor: string,    // 成功色
    warningColor: string,    // 警告色
    errorColor: string,      // 错误色
    infoColor: string        // 信息色
  },
  
  // 阴影配置
  shadows: {
    card: string,            // 卡片阴影
    dialog: string,          // 对话框阴影
    button: string           // 按钮阴影
  },
  
  // 渐变配置
  gradients: {
    primary: string,         // 主要渐变
    background: string,      // 背景渐变
    card: string            // 卡片渐变
  }
}
```

### 主题切换

系统会自动根据全局主题设置切换大屏主题：

```typescript
// 手动切换主题
themeStore.toggleThemeScheme(); // 循环切换 light -> dark -> auto

// 设置特定主题
themeStore.setThemeScheme('light'); // 亮色主题
themeStore.setThemeScheme('dark');  // 暗色主题
themeStore.setThemeScheme('auto');  // 自动主题
```

## 最佳实践

### 1. 样式隔离

为了避免影响全局样式，建议：

- 使用专用的 CSS 类名前缀（如 `bigscreen-`）
- 利用 Vue 的 `v-bind` 功能绑定主题变量
- 避免直接修改全局组件样式

### 2. 性能优化

- 主题变量使用 `computed` 计算属性，确保响应式更新
- 大量样式使用 CSS 变量而不是内联样式
- 合理使用 `transition` 实现平滑的主题切换效果

### 3. 扩展主题

如需添加新的主题预设：

```typescript
// 在 theme/index.ts 中添加新预设
export const bigscreenThemePresets = {
  // 现有预设...
  
  // 新增红色主题
  red: {
    light: {
      primary: '#dc2626',
      secondary: '#991b1b',
      background: '#2d1b1b',
      surface: '#4c1d1d'
    },
    dark: {
      primary: '#ef4444',
      secondary: '#dc2626',
      background: '#2d1b1b',
      surface: '#7f1d1d'
    }
  }
};
```

## 示例页面

项目中提供了完整的示例页面：

- `/bigscreen/home` - 大屏首页，展示主题系统的完整使用
- `/bigscreen-demo` - 主题演示页面，包含各种组件和交互

## 注意事项

1. **兼容性**: 确保项目中已安装并配置了 Naive UI
2. **主题变量**: 主题变量会自动注入到全局，可以在任何地方使用
3. **样式优先级**: 大屏专用样式具有更高的优先级，不会被全局样式覆盖
4. **响应式**: 主题切换是响应式的，会自动更新所有使用主题变量的组件

## 故障排除

### 主题不生效

1. 检查是否正确导入了 `useBigscreenTheme`
2. 确认 Vue 组件中正确使用了 `v-bind` 绑定主题变量
3. 检查 CSS 变量是否正确引用

### 样式冲突

1. 使用更具体的 CSS 选择器
2. 添加 `!important` 声明（谨慎使用）
3. 检查是否有全局样式覆盖了大屏样式

### 性能问题

1. 避免在模板中直接计算复杂的样式表达式
2. 使用 `computed` 属性缓存计算结果
3. 合理使用 CSS 变量减少重复计算
