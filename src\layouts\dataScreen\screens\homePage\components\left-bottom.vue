<template>
  <div class="boxCon">
    <div class="itemTitle">
      <span class="text gradient-text">易访客户</span>
    </div>
    <div class="flex-container">
      <div class="flex-container-rest">
        <div v-for="item in data" :key="item.title" class="flex-item">
          <div class="dataItem flx-center mb10 hoverIcon" @click="HandleCli(item.title)">
            <span class="dataName hoverIcon">{{ item.title }}</span>
            <b :class="getRowClasses(item.status)">{{ item.val }}</b>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

/**
 * 组件事件发射定义
 * @emits clickEvent - 点击数据项时触发的事件
 */
const emit = defineEmits(["clickEvent"]);

/**
 * 数据源
 * @type {Array<{title: string, val: number, status: string}>}
 */
const data = ref([
  { title: "易访用户数", val: 1167290, status: "down" },
  { title: "已触达数", val: 727831, status: "down" },
  { title: "确认友好客户数", val: 294412, status: "up" },
  { title: "低满待修复", val: 15688, status: "info" },
  { title: "修复完成数", val: 15090, status: "normal" },
  { title: "修复成功数", val: 10967, status: "up" },
  { title: "已质检量", val: 113229, status: "up" }
]);

/**
 * 处理点击事件
 * @param {string} item - 被点击项的标题
 */
const HandleCli = (item: any) => {
  emit("clickEvent", "易访库用户跟进", [item]);
};

/**
 * 状态样式映射
 */
const statusClasses = {
  down: ["warning-row"],
  up: ["success-row"],
  info: ["info-row"],
  default: ["default-row"]
};

/**
 * 获取行样式类
 * @returns {Function} - 返回一个函数，该函数接收状态并返回对应的样式类数组
 */
const getRowClasses = computed(() => {
  return status => {
    return ["dataValue", ...(statusClasses[status] || ["default-row"])];
  };
});
</script>

<style scoped lang="scss">
.boxCon {
  position: relative;
  flex: 1;
  padding: 10px;
  margin-top: 8px;
  border: 1px solid rgb(18 177 254 / 20%);
  border-top: none;
  .flex-container {
    display: flex;
    flex-wrap: wrap;
  }
  .flex-container-rest {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }
  .flex-item {
    box-sizing: border-box;
    flex: 1 1 50%;
    padding: 0 10px;
    &:first-child {
      flex: 1 1 100%;
    }
  }
  .dataItem {
    height: 32px;
    padding: 0 8px;
    cursor: pointer;
    background-image: linear-gradient(270deg, rgb(40 115 255 / 4%) 0%, rgb(40 115 255 / 16%) 99%);
    .dataName {
      font-family: YouSheBiaoTiHei;
      font-size: 18px;
      line-height: 20px;
      color: #ffffff;
    }
    &:hover {
      background: rgb(40 115 255 / 30%) 99%;
    }
  }
  .itemTitle {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 1px;
    &::before,
    &::after {
      display: inline-block;
      flex: 1;
      height: 1px;
      content: "";
      background: rgb(18 177 254 / 20%);
    }
    &::before {
      margin-right: 8px;
    }
    &::after {
      margin-left: 8px;
    }
    .text {
      position: relative;
      font-family: YouSheBiaoTiHei;
      font-size: 25px;
      &::before,
      &::after {
        position: absolute;
        top: 8px;
        display: inline-block;
        width: 2px;
        height: 6px;
        content: "";
        background: #fedb2c;
      }
      &::before {
        left: -8px;
      }
      &::after {
        right: -8px;
      }
    }
    .gradient-text {
      color: transparent;
      user-select: none;
      background: linear-gradient(to bottom, #ffffff, #85c0ed);
      background-clip: text;
    }
  }
  .hoverIcon {
    &::before,
    &::after {
      position: absolute;
      bottom: 0;
      box-sizing: border-box;
      display: inline-block;
      width: 6px;
      height: 6px;
      content: "";
    }
    &::before {
      left: 0;
      border-bottom: 1px solid #27d7ff;
      border-left: 1px solid #27d7ff;
    }
    &::after {
      right: 0;
      border-right: 1px solid #27d7ff;
      border-bottom: 1px solid #27d7ff;
    }
  }
}

// 状态行样式
.warning-row {
  color: #ff4d4f;
}
.success-row {
  color: #00ff88;
}
.info-row {
  color: #1890ff;
}
.default-row {
  color: #ffffff;
}
.dataValue {
  margin-left: auto;
  font-size: 16px;
  font-weight: bold;
}
</style>
