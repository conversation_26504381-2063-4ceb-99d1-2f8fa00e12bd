<script setup lang="ts">
import { ref, watch } from 'vue';
import { useBigscreenTheme } from '../../theme/index';

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'update:scale']);

const { bigscreenTheme, themeStore } = useBigscreenTheme();

const dialogVisible = ref(props.visible);
const scaleEnabled = ref(true);

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听内部visible变化，向父组件发送更新事件
watch(
  () => dialogVisible.value,
  val => {
    emit('update:visible', val);
  }
);

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 切换缩放效果
const toggleScale = () => {
  scaleEnabled.value = !scaleEnabled.value;
  emit('update:scale', scaleEnabled.value);
};
</script>

<template>
  <NModal
    v-model:show="dialogVisible"
    preset="card"
    title="大屏设置"
    class="settings-dialog w-500px"
    :bordered="false"
    :mask-closable="false"
  >
    <div class="flex flex-col gap-4">
      <!-- 缩放设置 -->
      <div
        class="flex items-center justify-between rounded-8px p-4"
        :style="{ backgroundColor: bigscreenTheme.colors.itemBg }"
      >
        <div class="flex items-center">
          <NIcon class="mr-2 text-xl" :style="{ color: bigscreenTheme.colors.textColor }">
            <SvgIcon icon="mdi:resize" />
          </NIcon>
          <span class="text-lg" :style="{ color: bigscreenTheme.colors.textColor }">自动缩放</span>
        </div>
        <NSwitch v-model:value="scaleEnabled" class="bigscreen-switch" @update:value="toggleScale" />
      </div>

      <!-- 主题设置 -->
      <div
        class="flex items-center justify-between rounded-8px p-4"
        :style="{ backgroundColor: bigscreenTheme.colors.itemBg }"
      >
        <div class="flex items-center">
          <NIcon class="mr-2 text-xl" :style="{ color: bigscreenTheme.colors.textColor }">
            <SvgIcon icon="mdi:palette" />
          </NIcon>
          <span class="text-lg" :style="{ color: bigscreenTheme.colors.textColor }">主题设置</span>
        </div>
        <div class="flex gap-2">
          <div
            class="h-24px w-24px cursor-pointer rounded-full transition-all hover:scale-110"
            :style="{ backgroundColor: bigscreenTheme.colors.buttonBg }"
            title="亮色主题"
            @click="themeStore.setThemeScheme('light')"
          ></div>
          <div
            class="h-24px w-24px cursor-pointer rounded-full transition-all hover:scale-110"
            :style="{ backgroundColor: bigscreenTheme.colors.successColor }"
            title="暗色主题"
            @click="themeStore.setThemeScheme('dark')"
          ></div>
          <div
            class="h-24px w-24px cursor-pointer rounded-full transition-all hover:scale-110"
            :style="{ backgroundColor: bigscreenTheme.colors.warningColor }"
            title="自动主题"
            @click="themeStore.setThemeScheme('auto')"
          ></div>
        </div>
      </div>

      <!-- 数据刷新间隔 -->
      <div
        class="flex items-center justify-between rounded-8px p-4"
        :style="{ backgroundColor: bigscreenTheme.colors.itemBg }"
      >
        <div class="flex items-center">
          <NIcon class="mr-2 text-xl" :style="{ color: bigscreenTheme.colors.textColor }">
            <SvgIcon icon="mdi:refresh" />
          </NIcon>
          <span class="text-lg" :style="{ color: bigscreenTheme.colors.textColor }">数据刷新间隔</span>
        </div>
        <NSelect
          class="w-120px bigscreen-select"
          :options="[
            { label: '10秒', value: 10 },
            { label: '30秒', value: 30 },
            { label: '1分钟', value: 60 },
            { label: '5分钟', value: 300 }
          ]"
          default-value="{60}"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <NButton
          class="bigscreen-button"
          :style="{
            backgroundColor: bigscreenTheme.colors.buttonBg,
            borderColor: bigscreenTheme.colors.borderColor,
            color: bigscreenTheme.colors.textColor
          }"
          @click="closeDialog"
        >
          关闭
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
.settings-dialog :deep(.n-card) {
  background-color: v-bind('bigscreenTheme.colors.dialogBg');
  border: 1px solid v-bind('bigscreenTheme.colors.borderColor');
  box-shadow: v-bind('bigscreenTheme.shadows.dialog');
}

.settings-dialog :deep(.n-card-header) {
  border-bottom: 1px solid v-bind('bigscreenTheme.colors.borderColor');
}

.settings-dialog :deep(.n-card-header__main) {
  color: v-bind('bigscreenTheme.colors.textColor');
  font-size: 18px;
}

.bigscreen-switch :deep(.n-switch) {
  --n-button-color-active: v-bind('bigscreenTheme.colors.buttonBg');
  --n-button-color: v-bind('bigscreenTheme.colors.inputBg');
  --n-rail-color: v-bind('bigscreenTheme.colors.inputBg');
  --n-rail-color-active: v-bind('bigscreenTheme.colors.buttonBg');
}

.bigscreen-select :deep(.n-base-selection) {
  background-color: v-bind('bigscreenTheme.colors.inputBg');
  border-color: v-bind('bigscreenTheme.colors.borderColor');
}

.bigscreen-select :deep(.n-base-selection-input) {
  color: v-bind('bigscreenTheme.colors.textColor');
}

.bigscreen-select :deep(.n-base-selection-placeholder) {
  color: v-bind('bigscreenTheme.colors.secondaryTextColor');
}

.bigscreen-button :deep(.n-button) {
  background-color: v-bind('bigscreenTheme.colors.buttonBg');
  border-color: v-bind('bigscreenTheme.colors.borderColor');
  color: v-bind('bigscreenTheme.colors.textColor');
  box-shadow: v-bind('bigscreenTheme.shadows.button');
  transition: all 0.3s ease;
}

.bigscreen-button:hover :deep(.n-button) {
  opacity: 0.8;
  transform: translateY(-1px);
}
</style>
