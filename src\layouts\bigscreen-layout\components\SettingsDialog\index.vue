<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible']);

const dialogVisible = ref(props.visible);
const scaleEnabled = ref(true);

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听内部visible变化，向父组件发送更新事件
watch(
  () => dialogVisible.value,
  val => {
    emit('update:visible', val);
  }
);

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 切换缩放效果
const toggleScale = () => {
  scaleEnabled.value = !scaleEnabled.value;
  emit('update:scale', scaleEnabled.value);
};
</script>

<template>
  <NModal
    v-model:show="dialogVisible"
    preset="card"
    title="大屏设置"
    class="settings-dialog w-500px"
    :bordered="false"
    :mask-closable="false"
  >
    <div class="flex flex-col gap-4">
      <!-- 缩放设置 -->
      <div class="flex items-center justify-between rounded-8px bg-[#051a3e] p-4">
        <div class="flex items-center">
          <NIcon class="mr-2 text-xl text-[#b1d8ff]">
            <SvgIcon icon="mdi:resize" />
          </NIcon>
          <span class="text-lg text-[#b1d8ff]">自动缩放</span>
        </div>
        <NSwitch v-model:value="scaleEnabled" @update:value="toggleScale" />
      </div>

      <!-- 其他设置项可以按照类似的方式添加 -->
      <div class="flex items-center justify-between rounded-8px bg-[#051a3e] p-4">
        <div class="flex items-center">
          <NIcon class="mr-2 text-xl text-[#b1d8ff]">
            <SvgIcon icon="mdi:palette" />
          </NIcon>
          <span class="text-lg text-[#b1d8ff]">主题设置</span>
        </div>
        <div class="flex gap-2">
          <div class="h-24px w-24px cursor-pointer rounded-full bg-[#1765c6]"></div>
          <div class="h-24px w-24px cursor-pointer rounded-full bg-[#1e9b4c]"></div>
          <div class="h-24px w-24px cursor-pointer rounded-full bg-[#9b1e1e]"></div>
        </div>
      </div>

      <div class="flex items-center justify-between rounded-8px bg-[#051a3e] p-4">
        <div class="flex items-center">
          <NIcon class="mr-2 text-xl text-[#b1d8ff]">
            <SvgIcon icon="mdi:refresh" />
          </NIcon>
          <span class="text-lg text-[#b1d8ff]">数据刷新间隔</span>
        </div>
        <NSelect
          class="w-120px"
          :options="[
            { label: '10秒', value: 10 },
            { label: '30秒', value: 30 },
            { label: '1分钟', value: 60 },
            { label: '5分钟', value: 300 }
          ]"
          default-value="{60}"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <NButton class="border-[#1765c6] bg-[#1765c6] text-[#b1d8ff]" @click="closeDialog">关闭</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
.settings-dialog :deep(.n-card) {
  background-color: #051a3e;
  border: 1px solid #1765c6;
}

.settings-dialog :deep(.n-card-header) {
  border-bottom: 1px solid #1765c6;
}

.settings-dialog :deep(.n-card-header__main) {
  color: #b1d8ff;
  font-size: 18px;
}

.settings-dialog :deep(.n-switch) {
  --n-button-color-active: #1765c6;
  --n-button-color: #0a3b7d;
  --n-rail-color: #0a3b7d;
  --n-rail-color-active: #1765c6;
}

.settings-dialog :deep(.n-base-selection) {
  background-color: #0a3b7d;
  border-color: #1765c6;
}

.settings-dialog :deep(.n-base-selection-input) {
  color: #b1d8ff;
}

.settings-dialog :deep(.n-base-selection-placeholder) {
  color: #8ab3e0;
}
</style>
