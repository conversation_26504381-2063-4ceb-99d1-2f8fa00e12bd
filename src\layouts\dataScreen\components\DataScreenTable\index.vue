<template>
  <div class="data-screen-table" ref="tableRef">
    <div class="corner-tr"></div>
    <div class="corner-bl"></div>
    <div class="corner-br"></div>

    <el-table
      ref="elTableRef"
      :data="tableData"
      :border="border"
      :show-summary="showSummary"
      :summary-method="getSummaryData"
      :cell-class-name="getCellClassName"
      :row-class-name="getRowClassName"
      :header-cell-class-name="getHeaderClassName"
      :highlight-current-row="true"
      :max-height="maxHeight"
      @row-click="handleRowClick"
      @mouseleave="handleMouseLeave"
      @mouseenter="handleMouseOver"
      v-bind="$attrs"
    >
      <template v-for="column in columns" :key="column.prop">
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :align="column.align || 'center'"
          :show-overflow-tooltip="column.showOverflowTooltip"
        >
          <template #default="scope" v-if="column.slotName">
            <slot :name="column.slotName" :row="scope.row" :index="scope.$index" :column="column"></slot>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script setup lang="ts">
/**
 * DataScreenTable - 数据大屏专用表格组件
 * 基于Element Plus表格组件，提供数据展示、自动滚动、合计行等功能
 */
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { ElTable, ElTableColumn } from "element-plus";
import { ColumnProps, SummaryMethodProps, CellClassNameProps, DataScreenTableInstance } from "./types";

/**
 * 组件属性接口
 */
interface Props {
  /** 表格列配置 */
  columns: ColumnProps[];
  /** 表格数据 */
  data: any[];
  /** 是否显示边框 */
  border?: boolean;
  /** 是否显示表尾合计行 */
  showSummary?: boolean;
  /** 合计计算方法 */
  summaryMethod?: (param: SummaryMethodProps) => any[];
  /** 自定义单元格类名方法 */
  cellClassName?: (param: CellClassNameProps) => string;
  /** 是否自动滚动 */
  autoScroll?: boolean;
  /** 滚动速度(毫秒) */
  scrollSpeed?: number;
  /** 滚动间隔(毫秒) */
  scrollInterval?: number;
  /** 表格最大高度 */
  maxHeight?: string | number;
}

// 定义组件属性的默认值
const props = withDefaults(defineProps<Props>(), {
  border: false,
  showSummary: false,
  autoScroll: true,
  scrollSpeed: 1,
  scrollInterval: 30,
  maxHeight: "100%"
});

// 表格引用
const tableRef = ref<HTMLElement | null>(null);
const elTableRef = ref<InstanceType<typeof ElTable> | null>(null);

// 数据
const tableData = computed(() => props.data);
const columns = computed(() => props.columns);

// 滚动定时器
let scrollTimer: any = null;
// 当前行索引
// const currentRowIndex = ref(0);

// 事件发射
const emit = defineEmits<{
  rowClick: [row: any];
}>();

/**
 * 获取单元格的CSS类名
 */
const getCellClassName = ({ row, column }: any) => {
  if (props.cellClassName) {
    const columnDef = props.columns.find(col => col.prop === column.property);
    if (columnDef) {
      return props.cellClassName({ column: columnDef, row }) + " hover-effect";
    }
  }

  // 默认类名处理，添加hover-effect类
  if (column.label === "部门") return "white-text hover-effect";
  if (column.label === "受理量" || column.label === "解决率") return "success-row hover-effect";
  return "default-row hover-effect";
};

/**
 * 获取行的CSS类名
 */
const getRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  return rowIndex % 2 === 0 ? "even-row" : "odd-row";
};

/**
 * 获取表头单元格的CSS类名
 */
const getHeaderClassName = () => {
  return "header-cell";
};

/**
 * 计算合计行数据
 */
const getSummaryData = (param: any) => {
  if (props.summaryMethod) {
    const adaptedParam: SummaryMethodProps = {
      columns: props.columns,
      data: props.data
    };
    return props.summaryMethod(adaptedParam);
  }

  // 默认合计行处理
  const { columns, data } = param;
  const sums: any[] = [];

  columns.forEach((column: any, index: number) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }

    const values = data.map((item: any) => Number(item[column.property]) || 0);
    sums[index] = values.reduce((prev: number, curr: number) => {
      const value = Number(curr);
      if (!isNaN(value)) {
        return prev + value;
      } else {
        return prev;
      }
    }, 0);

    // 处理百分比
    if (column.property.includes("率")) {
      sums[index] = Math.round((sums[index] / data.length) * 100) / 100 + "%";
    }
  });

  return sums;
};

/**
 * 行点击处理
 */
const handleRowClick = (row: any) => {
  emit("rowClick", row);
};

/**
 * 鼠标悬停处理 - 暂停滚动
 */
const handleMouseOver = () => {
  clearScroll();
};

/**
 * 鼠标移出处理 - 恢复滚动
 */
const handleMouseLeave = () => {
  if (props.autoScroll) {
    createScroll();
  }
};

// 声明Window接口扩展
declare global {
  interface Window {
    __tableScrollStarted?: boolean;
  }
}

/**
 * 创建表格滚动
 */
const createScroll = () => {
  // 使用nextTick确保DOM已经渲染
  nextTick(() => {
    // 先清除之前的滚动
    clearScroll();

    // 获取表格实例和DOM元素
    if (elTableRef.value) {
      const tableEl = elTableRef.value.$el;
      const bodyWrapper = tableEl.querySelector(".el-table__body-wrapper");

      if (bodyWrapper) {
        const tableBody = bodyWrapper.querySelector(".el-table__body");
        const rows = tableBody.querySelectorAll("tr");

        // 如果行数太少，不需要滚动
        if (rows.length <= 5) return;

        // 设置滚动速度和间隔
        const scrollSpeed = 0.5; // 滚动速度，值越小滚动越慢

        // 设置滚动定时器
        scrollTimer = setInterval(() => {
          // 增加滚动位置
          bodyWrapper.scrollTop += scrollSpeed;

          // 当第一行完全滚出视图时，将其移动到底部
          if (rows[0].offsetHeight && bodyWrapper.scrollTop >= rows[0].offsetHeight) {
            // 重置滚动位置
            bodyWrapper.scrollTop = 0;

            // 将第一行移到最后
            const firstRow = rows[0];
            tableBody.appendChild(firstRow);

            // 打印日志，用于调试
            console.log("行已循环", new Date().toLocaleTimeString());
          }
        }, 30); // 每30毫秒执行一次
      }
    }
  });
};

/**
 * 清除滚动
 */
const clearScroll = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer);
    scrollTimer = null;
  }
};

// 监听数据变化，重新启动滚动
watch(
  () => props.data,
  () => {
    if (props.autoScroll) {
      clearScroll();
      createScroll();
    }
  },
  { deep: true }
);

// 组件挂载时设置
onMounted(() => {
  // 如果自动滚动，启动滚动
  if (props.autoScroll) {
    // 增加延迟，确保DOM渲染完成并且表格内容已加载
    setTimeout(() => {
      // 如果表格已经渲染，则启动滚动
      if (elTableRef.value && elTableRef.value.$el) {
        // 启动滚动
        createScroll();

        // 控制台显示日志
        console.log("自动滚动已启动", new Date().toLocaleTimeString());
      } else {
        console.warn("表格尚未渲染，无法启动自动滚动");
      }
    }, 800); // 延长到800ms，确保有足够时间完成渲染
  }
});

// 组件卸载时清理
onUnmounted(() => {
  clearScroll();
});

/**
 * 外部调用方法：启动滚动
 */
const startScroll = () => {
  createScroll();
};

/**
 * 外部调用方法：停止滚动
 */
const stopScroll = () => {
  clearScroll();
};

// 对外暴露的方法
defineExpose<DataScreenTableInstance>({
  startScroll,
  stopScroll
});
</script>

<style lang="scss" scoped>
@import "./index";
</style>
