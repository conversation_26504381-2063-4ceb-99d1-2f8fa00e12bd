<template>
  <div class="normal-card">
    <div class="normal-card-header">
      <div class="normal-card-title">
        <div class="icon"></div>
        <span>{{ title }}</span>
      </div>
      <slot name="button"></slot>
    </div>
    <div class="normal-card-main">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 标准卡片组件属性
 */
interface Props {
  /**
   * 卡片标题
   */
  title: string;
}

// 定义组件属性
defineProps<Props>();
</script>

<style lang="scss" scoped>
// 样式已移至公共样式文件
</style>
