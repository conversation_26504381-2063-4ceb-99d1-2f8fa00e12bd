<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible']);

const dialogVisible = ref(props.visible);

// 监听visible属性变化
watch(
  () => props.visible,
  val => {
    dialogVisible.value = val;
  }
);

// 监听内部visible变化，向父组件发送更新事件
watch(
  () => dialogVisible.value,
  val => {
    emit('update:visible', val);
  }
);

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 发送消息
const message = ref('');
const chatMessages = ref([
  { role: 'system', content: '您好，我是AI助手，有什么可以帮助您的？', time: new Date().toLocaleTimeString() }
]);

const sendMessage = () => {
  if (!message.value.trim()) return;

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content: message.value,
    time: new Date().toLocaleTimeString()
  });

  // 模拟AI回复
  setTimeout(() => {
    chatMessages.value.push({
      role: 'system',
      content: '我正在处理您的问题，请稍候...',
      time: new Date().toLocaleTimeString()
    });
  }, 500);

  // 清空输入框
  message.value = '';
};
</script>

<template>
  <NModal
    v-model:show="dialogVisible"
    preset="card"
    title="智能助手"
    class="ai-dialog w-500px"
    :bordered="false"
    :mask-closable="false"
  >
    <div class="h-550px flex flex-col">
      <!-- 对话内容区域 -->
      <div class="flex-1 overflow-auto rounded-8px bg-[#051a3e] p-4">
        <div
          v-for="(msg, index) in chatMessages"
          :key="index"
          class="mb-4"
          :class="msg.role === 'user' ? 'flex flex-row-reverse' : 'flex'"
        >
          <!-- 头像 -->
          <div
            class="mr-2 h-40px w-40px flex flex-shrink-0 items-center justify-center rounded-full"
            :class="msg.role === 'user' ? 'bg-[#1765c6]' : 'bg-[#0a3b7d]'"
          >
            <SvgIcon :icon="msg.role === 'user' ? 'carbon:user-avatar' : 'mdi:robot'" class="text-xl text-[#b1d8ff]" />
          </div>

          <!-- 消息内容 -->
          <div class="max-w-[70%] rounded-8px p-3" :class="msg.role === 'user' ? 'bg-[#1765c6]' : 'bg-[#0a3b7d]'">
            <div class="text-[#b1d8ff]">{{ msg.content }}</div>
            <div class="mt-1 text-right text-xs text-[#8ab3e0]">{{ msg.time }}</div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="mt-3 h-60px flex items-center">
        <NInput
          v-model:value="message"
          type="text"
          placeholder="请输入您的问题..."
          class="mr-2 flex-1"
          @keyup.enter="sendMessage"
        >
          <template #prefix>
            <NIcon>
              <SvgIcon icon="mdi:message-text" class="text-[#b1d8ff]" />
            </NIcon>
          </template>
        </NInput>
        <NButton type="primary" class="border-[#1765c6] bg-[#1765c6]" @click="sendMessage">
          <template #icon>
            <NIcon>
              <SvgIcon icon="mdi:send" class="text-[#b1d8ff]" />
            </NIcon>
          </template>
          发送
        </NButton>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <NButton class="border-[#1765c6] bg-[#1765c6] text-[#b1d8ff]" @click="closeDialog">关闭</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped>
.ai-dialog :deep(.n-card) {
  background-color: #051a3e;
  border: 1px solid #1765c6;
}

.ai-dialog :deep(.n-card-header) {
  border-bottom: 1px solid #1765c6;
}

.ai-dialog :deep(.n-card-header__main) {
  color: #b1d8ff;
  font-size: 18px;
}

.ai-dialog :deep(.n-input) {
  background-color: #0a3b7d;
  border-color: #1765c6;
}

.ai-dialog :deep(.n-input__input) {
  color: #b1d8ff;
}

.ai-dialog :deep(.n-input__placeholder) {
  color: #8ab3e0;
}
</style>
