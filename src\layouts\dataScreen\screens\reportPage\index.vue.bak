<template>
  <div class="report-page">
    <div class="report-container">
      <!-- 顶部导航区域 -->
      <div class="header-section">
        <div class="nav-left">
          <el-button v-if="showTable" class="back-btn" @click="handleBackToChart">
            <el-icon><ArrowLeft /></el-icon>
            返回总览
          </el-button>
          <div v-if="showTable" class="nav-breadcrumb-wrapper">
            <div class="nav-breadcrumb" ref="breadcrumbRef">
              <el-breadcrumb separator="/">
                <!-- 始终显示首层 -->
                <el-breadcrumb-item :class="{ 'is-link': breadcrumb.length > 1 }" @click="handleBreadcrumbClick(0)">
                  {{ breadcrumb[0].name }}
                </el-breadcrumb-item>

                <!-- 如果层级过多，显示省略号 -->
                <el-breadcrumb-item v-if="showEllipsis" class="ellipsis"> ... </el-breadcrumb-item>

                <!-- 显示最后两层 -->
                <template v-for="(item, index) in visibleBreadcrumbs" :key="index">
                  <el-breadcrumb-item
                    :class="{ 'is-link': index !== visibleBreadcrumbs.length - 1 }"
                    @click="handleBreadcrumbClick(getBreadcrumbRealIndex(index))"
                  >
                    {{ item.name }}
                  </el-breadcrumb-item>
                </template>
              </el-breadcrumb>
            </div>
          </div>
          <div v-else class="page-title">区域网络质量分析</div>
        </div>
        <div class="current-area" :title="currentArea.name">当前区域：{{ formatAreaName(currentArea.name) }}</div>
      </div>

      <!-- 图表区域 -->
      <div v-if="!showTable" class="charts-section">
        <div class="charts-header">
          <div class="title">区域网络质量分析</div>
          <div class="time-select">
            <el-select v-model="timeType" class="custom-select" @change="handleTimeTypeChange">
              <el-option label="季度" value="quarter" />
              <el-option label="期" value="period" />
            </el-select>
          </div>
        </div>
        <div class="charts-content" :class="{ 'vertical-layout': timeType === 'period' }">
          <!-- 上网质量满意度 -->
          <div class="chart-item">
            <div class="chart-header">
              <div class="title">上网质量满意度</div>
              <div class="value">{{ networkQualityAvg }}<span class="unit">%</span></div>
            </div>
            <div class="chart-content" ref="networkChartContainer">
              <BarChart
                v-if="!loading && networkQualityData.xAxis.length > 0"
                ref="networkQualityChart"
                :key="`network-${timeType}`"
                :data="networkQualityData"
                :bar-width="getBarWidth"
                :color="networkQualityColors"
                :show-y-axis="false"
                :height="networkChartContainer?.clientHeight"
                @click="handleChartClick"
              />
              <div v-else-if="loading" class="loading-wrapper">
                <el-icon class="loading-icon" :size="24"><Loading /></el-icon>
              </div>
            </div>
          </div>

          <!-- 装维服务满意度 -->
          <div class="chart-item">
            <div class="chart-header">
              <div class="title">装维服务满意度</div>
              <div class="value">{{ serviceQualityAvg }}<span class="unit">%</span></div>
            </div>
            <div class="chart-content" ref="serviceChartContainer">
              <BarChart
                v-if="!loading && serviceQualityData.xAxis.length > 0"
                ref="serviceQualityChart"
                :key="`service-${timeType}`"
                :data="serviceQualityData"
                :bar-width="getBarWidth"
                :color="serviceQualityColors"
                :show-y-axis="false"
                :height="serviceChartContainer?.clientHeight"
                @click="handleChartClick"
              />
              <div v-else-if="loading" class="loading-wrapper">
                <el-icon class="loading-icon" :size="24"><Loading /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格区域 -->
      <div v-else class="table-section">
        <DataTable :columns="columns" :data="tableData" @row-click="handleRowClick" :row-class="getRowClass">
          <template #header>
            <div class="table-header">
              <span class="title">数据明细</span>
              <div class="actions">
                <el-button type="primary" @click="handleExport">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-button>
              </div>
            </div>
          </template>

          <template #trend="{ row }">
            <el-tag :type="row.trend === 'up' ? 'success' : 'danger'">
              {{ row.trend === "up" ? "↑" : "↓" }}
              {{ row.changeRate }}%
            </el-tag>
          </template>
        </DataTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";
import { ArrowLeft, Download, Loading } from "@element-plus/icons-vue";
// import DataTable from "@/components/DataTable/index.vue";
// import BarChart from "@/components/ECharts/charts/BarChart.vue";
// import { getProvinceData, getCityData, getDistrictData, getQuarterlyData } from "@/api/modules/race/report";

// 是否显示表格
const showTable = ref(false);

// 图表实例引用
const networkQualityChart = ref();
const serviceQualityChart = ref();
const networkChartContainer = ref<HTMLElement>();
const serviceChartContainer = ref<HTMLElement>();

// 图表数据
const chartData = ref<any>(null);

// 计算平均值
const networkQualityAvg = computed(() => {
  const values = chartData.value?.networkQuality?.map(item => item.value) || [];
  if (!values.length) return "0.0";
  const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
  return avg.toFixed(1);
});

const serviceQualityAvg = computed(() => {
  const values = chartData.value?.serviceQuality?.map(item => item.value) || [];
  if (!values.length) return "0.0";
  const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
  return avg.toFixed(1);
});

// 加载状态
const loading = ref(true);

// 图表数据处理
const networkQualityData = computed(() => {
  if (!chartData.value?.networkQuality?.length) {
    return { xAxis: [], series: [] };
  }
  const data = {
    xAxis: chartData.value.networkQuality.map(item => item.name),
    series: chartData.value.networkQuality.map(item => item.value)
  };
  console.log("Network Quality Chart Data:", data);
  return data;
});

const serviceQualityData = computed(() => {
  if (!chartData.value?.serviceQuality?.length) {
    return { xAxis: [], series: [] };
  }
  const data = {
    xAxis: chartData.value.serviceQuality.map(item => item.name),
    series: chartData.value.serviceQuality.map(item => item.value)
  };
  console.log("Service Quality Chart Data:", data);
  return data;
});

// 图表颜色配置
const networkQualityColors = [
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      { offset: 0, color: "rgba(84, 128, 255, 0.8)" },
      { offset: 1, color: "rgba(84, 128, 255, 0.3)" }
    ]
  }
];

const serviceQualityColors = [
  {
    type: "linear",
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      { offset: 0, color: "rgba(0, 255, 146, 0.8)" },
      { offset: 1, color: "rgba(0, 255, 146, 0.3)" }
    ]
  }
];

// 处理图表点击
const handleChartClick = (params: any) => {
  if (!chartData.value?.networkQuality) return;
  const clickedData = chartData.value.networkQuality[params.dataIndex];
  if (!clickedData) return;

  showTable.value = true;
  // 更新面包屑和当前区域
  breadcrumb.value = [{ code: clickedData.code, name: clickedData.name }];
  currentArea.value = { code: clickedData.code, name: clickedData.name };
  // 更新表格数据
  updateTableData(clickedData);
};

// 返回图表视图
const handleBackToChart = async () => {
  showTable.value = false;
  breadcrumb.value = [{ code: "CS", name: "长沙市" }];
  currentArea.value = { code: "CS", name: "长沙市" };

  // 重新获取省级数据
  const res: any = await getProvinceData();
  if (res.code === 200) {
    tableData.value = res.data;
  }
};

// 初始化数据
onMounted(async () => {
  const res: any = await getProvinceData();
  if (res.code === 200) {
    tableData.value = res.data;
  }
});

// 定义不同层级的数据类型
interface ProvinceData {
  name: string;
  totalScore: number;
  networkQuality: number;
  coverage: number;
  trend: "up" | "down";
  changeRate: number;
  code: string;
}

interface CityData {
  name: string;
  totalScore: number;
  satisfaction: number;
  complaintRate: number;
  trend: "up" | "down";
  changeRate: number;
  code: string;
  extraLongField1: string;
  extraLongField2: string;
  extraLongField3: string;
}

interface DistrictData {
  name: string;
  signalStrength: number;
  userCount: number;
  speedTest: string;
  trend: "up" | "down";
  changeRate: number;
  code: string;
}

// 使用联合类型
type TableDataType = ProvinceData | CityData | DistrictData;

// 添加以下接口定义
interface Column {
  prop: string;
  label: string;
  align?: string;
  minWidth?: number;
  formatter?: (value: any) => string;
  wrap?: boolean;
  slot?: boolean;
}

interface ApiResponse<T> {
  code: number;
  data: T;
}

interface ChartData {
  networkQuality: {
    name: string;
    value: number;
    code: string;
  }[];
  serviceQuality: {
    name: string;
    value: number;
    code: string;
  }[];
}

// 定义列配置工厂函数
const createColumn = (options: Partial<Column>): Column => {
  return {
    align: "center",
    minWidth: 120,
    ...options,
    prop: options.prop!,
    label: options.label!
  };
};

// 列配置
const columnConfigs = {
  province: [
    createColumn({
      prop: "name",
      label: "城市名称"
    }),
    createColumn({
      prop: "totalScore",
      label: "总评分",
      formatter: value => `${value}分`
    }),
    createColumn({
      prop: "networkQuality",
      label: "网络质量",
      formatter: value => `${value}%`
    }),
    createColumn({
      prop: "coverage",
      label: "覆盖范围",
      formatter: value => `${value}%`
    }),
    createColumn({
      prop: "trend",
      label: "环比趋势",
      slot: true
    })
  ],
  city: [
    createColumn({
      prop: "name",
      label: "区县名称"
    }),
    createColumn({
      prop: "totalScore",
      label: "总评分",
      formatter: value => `${value}分`
    }),
    createColumn({
      prop: "satisfaction",
      label: "用户满意度",
      formatter: value => `${value}%`
    }),
    createColumn({
      prop: "complaintRate",
      label: "投诉率",
      formatter: value => `${value}%`
    }),
    createColumn({
      prop: "extraLongField1",
      label: "超长测试字段1",
      wrap: true,
      formatter: value => value
    }),
    createColumn({
      prop: "extraLongField2",
      label: "超长测试字段2",
      formatter: value => value
    }),
    createColumn({
      prop: "extraLongField3",
      label: "超长测试字段3",
      formatter: value => value
    }),
    createColumn({
      prop: "trend",
      label: "环比趋势",
      slot: true
    })
  ],
  district: [
    createColumn({
      prop: "name",
      label: "街道名称"
    }),
    createColumn({
      prop: "signalStrength",
      label: "信号强度",
      formatter: value => `${value}%`
    }),
    createColumn({
      prop: "userCount",
      label: "用户数量",
      formatter: value => value
    }),
    createColumn({
      prop: "speedTest",
      label: "网速测试",
      formatter: value => value
    }),
    createColumn({
      prop: "trend",
      label: "环比趋势",
      slot: true
    })
  ]
};

// 使用 ref 来存储当前的列配置和数据
const columns = ref<any>(columnConfigs.city);
const tableData = ref<TableDataType[]>([]);

// 面包屑导航
const breadcrumb = ref([{ code: "CS", name: "长沙市" }]);

// 当前区域
const currentArea = ref({ code: "CS", name: "长沙市" });

// 计算是否显示省略号
const showEllipsis = computed(() => {
  return breadcrumb.value.length > 4;
});

// 计算需要显示的面包屑项
const visibleBreadcrumbs = computed(() => {
  if (breadcrumb.value.length <= 3) {
    return breadcrumb.value.slice(1);
  }
  // 只显示最后两层
  return breadcrumb.value.slice(-2);
});

// 获取面包屑真实索引
const getBreadcrumbRealIndex = (visibleIndex: number) => {
  if (breadcrumb.value.length <= 3) {
    return visibleIndex + 1;
  }
  return breadcrumb.value.length - 2 + visibleIndex;
};

// 定义最大钻取层级
const MAX_LEVEL = 3;

// 判断是否可以继续取
const canDrill = (level: number) => {
  return level < MAX_LEVEL;
};

// 获取行的类名
const getRowClass = (row: any) => {
  return {
    "no-more-drill": !canDrill(breadcrumb.value.length),
    "can-drill": canDrill(breadcrumb.value.length)
  };
  row;
};

// 修改理行点击方法
const handleRowClick = (row: TableDataType) => {
  // 如果已经到达最大层级，则不处理点击事件
  if (!canDrill(breadcrumb.value.length)) {
    return;
  }

  breadcrumb.value.push({
    code: row.code,
    name: row.name
  });
  currentArea.value = {
    code: row.code,
    name: row.name
  };

  // 根据层级更新列配置
  updateColumns(breadcrumb.value.length);

  // 更新表格数据
  updateTableData(row);
};

// 修改面包屑点击处理方法
const handleBreadcrumbClick = (index: number) => {
  if (index === breadcrumb.value.length - 1) return;

  breadcrumb.value = breadcrumb.value.slice(0, index + 1);
  currentArea.value = {
    code: breadcrumb.value[index].code,
    name: breadcrumb.value[index].name
  };

  // 根据层级更新列配置
  updateColumns(index + 1);

  // 更新表格数据
  if (index === 0) {
    updateTableData(null, true);
  } else {
    updateTableData(breadcrumb.value[index]);
  }
};

// 更新列配置的方法
const updateColumns = (level: number) => {
  switch (level) {
    case 1:
      columns.value = columnConfigs.province;
      break;
    case 2:
      columns.value = columnConfigs.city;
      break;
    case 3:
      columns.value = columnConfigs.district;
      break;
    default:
      columns.value = columnConfigs.district;
  }
};

// 更新表格数据的方法
const updateTableData = async (row: any, isRoot = false) => {
  try {
    if (isRoot) {
      // 获取省级数据
      const res: any = await getProvinceData();
      if (res.code === 200) {
        tableData.value = res.data;
      }
    } else {
      const level = breadcrumb.value.length;
      if (level === 2) {
        // 获取市级数据
        const res: any = await getCityData(row.code);
        if (res.code === 200) {
          tableData.value = res.data;
        }
      } else if (level === 3) {
        // 获取区县级数据
        const res: any = await getDistrictData(row.code);
        if (res.code === 200) {
          tableData.value = res.data;
        }
      }
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

// 导出数据
const handleExport = () => {
  // 实现导出逻辑
  console.log("导出数据");
};

// 添加区域名称格式化方法
const formatAreaName = (name: string) => {
  if (name.length > 10) {
    return name.slice(0, 10) + "...";
  }
  return name;
};

// 时间类型（季度/期）
const timeType = ref("quarter");

// 处理时间类型切换
const handleTimeTypeChange = async (type: string) => {
  timeType.value = type;
  // 切换时先重置数据
  chartData.value = null;
  // 延迟一帧再加载数据，确保布局已更新
  await nextTick();
  const defaultPeriod = type === "quarter" ? "Q1" : "P1";
  await loadChartData(defaultPeriod);
};

// 加载图表数据
const loadChartData = async (period?: string) => {
  try {
    loading.value = true;
    const currentPeriod = period || (timeType.value === "quarter" ? "Q1" : "P1");
    console.log("Loading data for period:", currentPeriod);
    const res = (await getQuarterlyData(currentPeriod)) as ApiResponse<ChartData>;
    if (res.code === 200) {
      console.log("Received data:", res.data);
      chartData.value = res.data;

      // 等待DOM更新完成
      await nextTick();

      // 检查图表容器尺寸
      if (networkChartContainer.value) {
        console.log(
          "Network chart container size:",
          networkChartContainer.value.clientWidth,
          networkChartContainer.value.clientHeight
        );
      }
      if (serviceChartContainer.value) {
        console.log(
          "Service chart container size:",
          serviceChartContainer.value.clientWidth,
          serviceChartContainer.value.clientHeight
        );
      }
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 初始化时加载���据
onMounted(() => {
  // 直接加载数据，不需要延迟
  loadChartData("Q1");
});

// 根据布局计算柱子宽度
const getBarWidth = computed(() => {
  // 期数据时使用更宽的柱子，但不要太宽
  return timeType.value === "period" ? 120 : 60;
});
</script>

<style lang="scss" scoped>
.report-page {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.04% 2.08%;
  color: #ffffff;

  // background: linear-gradient(180deg, rgb(16 23 42 / 100%) 0%, rgb(11 19 36 / 100%) 100%);
  .report-container {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 1.25%;
    height: 100%;
  }

  // 顶部导航区域
  .header-section {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
    padding: 0.83% 1.25%;
    background: linear-gradient(90deg, rgb(22 31 56 / 60%) 0%, rgb(32 41 66 / 60%) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgb(84 128 255 / 20%);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgb(0 0 0 / 15%);
    .nav-left {
      display: flex;
      flex: 1;
      gap: 16px;
      align-items: center;
      min-width: 0;
      .back-btn {
        z-index: 2;
        display: flex;
        flex-shrink: 0;
        gap: 4px;
        align-items: center;
        padding: 8px 16px;
        font-size: 14px;
        color: #ffffff;
        background: rgb(84 128 255 / 15%);
        border: 1px solid rgb(84 128 255 / 30%);
        border-radius: 4px;
        transition: all 0.3s ease;
        .el-icon {
          font-size: 16px;
        }
        &:hover {
          background: rgb(84 128 255 / 25%);
          border-color: rgb(84 128 255 / 50%);
          transform: translateX(-2px);
        }
      }
      .nav-breadcrumb-wrapper {
        flex: 1;
        min-width: 0;
        margin-right: 20px;
        overflow: hidden;
      }
      .nav-breadcrumb {
        width: 100%;
        overflow: hidden;
        :deep(.el-breadcrumb) {
          display: flex;
          align-items: center;
          white-space: nowrap;
          .el-breadcrumb__item {
            display: flex;
            flex-shrink: 0;
            align-items: center;

            // 修改这部分样式
            &:not(:last-child) {
              .el-breadcrumb__inner {
                cursor: pointer; // 为非最后一项添加指针样式
              }
            }
            .el-breadcrumb__inner {
              font-weight: 500;
              color: rgb(255 255 255 / 85%);
              transition: all 0.3s ease;
              &.is-link {
                color: #5480ff;
                cursor: pointer; // 这里保留
                &:hover {
                  color: #7699ff;
                  text-shadow: 0 0 8px rgb(84 128 255 / 40%);
                }
              }
            }
            &.ellipsis {
              .el-breadcrumb__inner {
                padding: 0 4px;
                color: rgb(255 255 255 / 50%);
                cursor: default; // 省略号保持默认鼠样式
                &:hover {
                  color: rgb(255 255 255 / 50%);
                }
              }
            }
            .el-breadcrumb__separator {
              margin: 0 8px;
              color: rgb(255 255 255 / 50%);
            }
          }
        }
      }
    }
    .current-area {
      z-index: 1;
      flex-shrink: 0;
      max-width: 200px;
      padding: 6px 12px;
      overflow: hidden;
      font-size: 14px;
      color: #87cdff;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: default;
      background: rgb(84 128 255 / 10%);
      border-radius: 4px;
      &:hover {
        background: rgb(84 128 255 / 15%);
      }
    }
  }

  // 表格区域
  .table-section {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 1.25%;
    background: linear-gradient(135deg, rgb(22 31 56 / 60%) 0%, rgb(32 41 66 / 1%) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgb(84 128 255 / 20%);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgb(0 0 0 / 15%);
    .table-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px;
      margin-bottom: 20px;
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        text-shadow: 0 0 10px rgb(84 128 255 / 20%);
      }
      .actions {
        :deep(.el-button) {
          display: flex;
          gap: 4px;
          align-items: center;
          padding: 8px 16px;
          font-weight: 500;
          color: #ffffff;
          background: rgb(84 128 255 / 20%);
          border: 1px solid rgb(84 128 255 / 30%);
          transition: all 0.3s ease;
          .el-icon {
            font-size: 16px;
          }
          &:hover {
            background: rgb(84 128 255 / 30%);
            border-color: rgb(84 128 255 / 40%);
          }
          &:active {
            background: rgb(84 128 255 / 40%);
          }
        }
      }
    }
    :deep(.el-table) {
      position: relative;
      transform: none !important;
    }
    :deep(.can-drill) {
      cursor: pointer;
      &:hover {
        background: rgb(18 142 255 / 15%);
      }
    }
    :deep(.no-more-drill) {
      cursor: not-allowed;
      opacity: 0.8;
      &:hover {
        background: rgb(18 142 255 / 8%) !important;
      }
      td {
        &:hover {
          color: rgb(255 255 255 / 85%) !important;
        }
      }
    }
  }
  .page-title {
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
  }
  .charts-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 20px;
    background: linear-gradient(135deg, rgb(22 31 56 / 60%) 0%, rgb(32 41 66 / 60%) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgb(84 128 255 / 20%);
    border-radius: 12px;
    .charts-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px 20px;
      .title {
        font-size: 20px;
        font-weight: 600;
        color: #ffffff;
      }
      .time-select {
        .custom-select {
          width: 120px;
          :deep(.el-input__wrapper) {
            background: rgb(84 128 255 / 15%);
            border: 1px solid rgb(84 128 255 / 30%);
            box-shadow: none;
            .el-input__inner {
              height: 32px;
              font-size: 14px;
              color: #ffffff;
              &::placeholder {
                color: rgb(255 255 255 / 50%);
              }
            }
            &:hover {
              background: rgb(84 128 255 / 20%);
              border-color: rgb(84 128 255 / 40%);
            }
            &.is-focus {
              background: rgb(84 128 255 / 25%);
              border-color: rgb(84 128 255 / 50%);
            }
          }
        }
      }
    }
    .charts-content {
      display: flex;
      flex: 1;
      gap: 20px;
      min-height: 0;
      transition: all 0.3s ease;

      // 垂直布局样式
      &.vertical-layout {
        flex-direction: column;
        .chart-item {
          height: calc(50% - 10px); // 改为50%
          min-height: 250px; // 添加最小高度
          .chart-content {
            height: calc(100% - 52px); // 减去header高度
          }
        }
      }
      .chart-item {
        display: flex;
        flex: 1;
        flex-direction: column;
        min-width: 0; // 添加这行
        padding: 20px;
        background: rgb(16 23 42 / 40%);
        border: 1px solid rgb(84 128 255 / 20%);
        border-radius: 8px;
        transition: all 0.3s ease;
        .chart-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          .title {
            font-size: 16px;
            color: rgb(255 255 255 / 85%);
          }
          .value {
            font-size: 24px;
            font-weight: 600;
            color: #5480ff;
            .unit {
              margin-left: 4px;
              font-size: 14px;
              font-weight: normal;
              opacity: 0.8;
            }
          }
        }
        .chart-content {
          position: relative;
          flex: 1;
          width: 100%;
          height: 100%;
          min-height: 200px;
          border: 1px solid rgb(255 255 255 / 10%);
        }
      }
    }
  }
}

// 下拉框样式
:deep(.el-select__popper) {
  background: rgb(16 23 42 / 95%);
  backdrop-filter: blur(8px);
  border: 1px solid rgb(84 128 255 / 30%);
  .el-select-dropdown__item {
    color: #ffffff;
    &:hover {
      background-color: rgb(84 128 255 / 15%);
    }
    &.selected {
      color: #5480ff;
      background-color: rgb(84 128 255 / 30%);
    }
  }
}
.loading-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  .loading-icon {
    color: #5480ff;
    animation: rotate 1s linear infinite;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
