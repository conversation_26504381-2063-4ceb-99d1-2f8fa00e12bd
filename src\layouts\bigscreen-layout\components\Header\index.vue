<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useFullscreen } from '@vueuse/core';
import AiDialog from '../AiDialog/index.vue';
import SettingsDialog from '../SettingsDialog/index.vue';

interface Props {
  title?: string;
}
defineProps<Props>();
const emit = defineEmits(['update:scale']);

const router = useRouter();
const { isFullscreen, toggle } = useFullscreen();
const aiVisible = ref(false);
const settingsVisible = ref(false);
const scaleEnabled = ref(true);

const menuItems = [
  { key: 'home', label: '首页' },
  { key: 'follow', label: '关注' },
  { key: 'recommend', label: '推荐' },
  { key: 'hot', label: '热门' },
  { key: 'local', label: '本地' }
];

const goTo = (key: string) => {
  router.push({ name: key });
};

/**
 * 打开AI对话框
 */
const openAiWindow = () => {
  aiVisible.value = true;
};

/**
 * 打开设置对话框
 */
const openSettingsWindow = () => {
  settingsVisible.value = true;
};

/**
 * 处理缩放设置变更
 */
const handleScaleChange = (value: boolean) => {
  scaleEnabled.value = value;
  emit('update:scale', value);
};
</script>

<template>
  <div
    class="h-75px w-full flex items-center justify-between bg-[length:100%_100%] bg-[url('@/assets/bigscreen/header-bg.png')] bg-center bg-no-repeat px-20px"
  >
    <!-- 左侧区域 -->
    <div class="w-[40%] flex items-center">
      <img src="@/assets/bigscreen/logo.png" class="mr-8px h-50px" alt="logo" />
      <div class="flex items-center">
        <template v-for="(item, index) in menuItems" :key="item.key">
          <NButton text type="primary" @click="goTo(item.key)">
            <span class="text-xl text-[#b1d8ff] hover:underline">{{ item.label }}</span>
          </NButton>
          <div
            v-if="index !== menuItems.length - 1"
            class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b"
          />
        </template>
      </div>
    </div>

    <!-- 中间标题 -->
    <div class="text-center">
      <h1 class="text-4xl text-white font-bold leading-46px tracking-6px shadow-[0_6px_20px_rgba(1,48,108,0.62)]">
        {{ title }}
      </h1>
    </div>

    <!-- 右侧功能区 -->
    <div class="w-[40%] flex items-center justify-end space-x-4">
      <NButton text type="primary" class="mx-2 px-16px" @click="openAiWindow">
        <template #icon>
          <img src="@/assets/bigscreen/robot.png" class="mr-2 h-50px max-w-100px" />
        </template>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="flex flex-col px-16px" @click="openAiWindow">
        <template #icon>
          <NIcon>
            <SvgIcon icon="mdi:calendar-clock" class="text-xl text-[#b1d8ff]" />
          </NIcon>
        </template>
        <span class="text-lg text-[#b1d8ff] hover:underline">调度台</span>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="flex flex-col px-16px" @click="openAiWindow">
        <template #icon>
          <NIcon>
            <SvgIcon icon="ion:desktop-outline" class="text-xl text-[#b1d8ff]" />
          </NIcon>
        </template>
        <span class="text-lg text-[#b1d8ff] hover:underline">工作台</span>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="pl-10px pr-10px" @click="openSettingsWindow">
        <template #icon>
          <NIcon class="text-3xl text-[#b1d8ff]">
            <SvgIcon icon="mdi:cog" />
          </NIcon>
        </template>
      </NButton>

      <div class="mx-10px h-24px w-1px from-[#5b70aa] to-transparent bg-gradient-to-b" />

      <NButton text type="primary" class="pl-10px pr-10px" @click="toggle">
        <template #icon>
          <NIcon class="text-3xl text-[#b1d8ff]">
            <SvgIcon v-if="isFullscreen" icon="gridicons-fullscreen-exit" />
            <SvgIcon v-else icon="gridicons-fullscreen" />
          </NIcon>
        </template>
      </NButton>
    </div>
  </div>

  <!-- AI对话框 -->
  <AiDialog v-model:visible="aiVisible" />
  <!-- 设置对话框 -->
  <SettingsDialog v-model:visible="settingsVisible" @update:scale="handleScaleChange" />
</template>
