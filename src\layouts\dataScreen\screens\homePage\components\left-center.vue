<template>
  <div class="table-container">
    <DataScreenTable
      ref="dataTable"
      :columns="columns"
      :data="mockData"
      :show-summary="true"
      :summary-method="getSummaries"
      :cell-class-name="tableColClassName"
      :auto-scroll="true"
      :max-height="'250px'"
      @row-click="rowClick"
      :key="activeTab"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, withDefaults, watch, onMounted } from "vue";
import { DataScreenTable } from "@/views/dataScreen/components";
import { ColumnProps, SummaryMethodProps } from "@/views/dataScreen/components/DataScreenTable/types";

interface Props {
  activeTab: string;
}

const props = withDefaults(defineProps<Props>(), { activeTab: "年" });

// 表格引用
const dataTable = ref();

// 扩展模拟数据
const mockData = ref([
  {
    主责部门: "技术部",
    全年受理总量: 1200,
    全年累计解决量: 1150,
    全年累计解决率: "95.8%",
    当月受理量: 120,
    当月解决量: 115,
    当月解决率: "95.8%",
    超时工单: 5,
    当月不满意工单: 3
  },
  {
    主责部门: "客服部",
    全年受理总量: 2500,
    全年累计解决量: 2400,
    全年累计解决率: "96.0%",
    当月受理量: 220,
    当月解决量: 210,
    当月解决率: "95.5%",
    超时工单: 8,
    当月不满意工单: 4
  },
  {
    主责部门: "运维部",
    全年受理总量: 800,
    全年累计解决量: 780,
    全年累计解决率: "97.5%",
    当月受理量: 80,
    当月解决量: 78,
    当月解决率: "97.5%",
    超时工单: 2,
    当月不满意工单: 1
  },
  {
    主责部门: "产品部",
    全年受理总量: 600,
    全年累计解决量: 580,
    全年累计解决率: "96.7%",
    当月受理量: 60,
    当月解决量: 58,
    当月解决率: "96.7%",
    超时工单: 3,
    当月不满意工单: 2
  },
  {
    主责部门: "产品部",
    全年受理总量: 600,
    全年累计解决量: 580,
    全年累计解决率: "96.7%",
    当月受理量: 60,
    当月解决量: 58,
    当月解决率: "96.7%",
    超时工单: 3,
    当月不满意工单: 2
  },
  {
    主责部门: "产品部",
    全年受理总量: 600,
    全年累计解决量: 580,
    全年累计解决率: "96.7%",
    当月受理量: 60,
    当月解决量: 58,
    当月解决率: "96.7%",
    超时工单: 3,
    当月不满意工单: 2
  }
]);

const emit = defineEmits(["clickEvent"]);

onMounted(() => {
  console.log("DataScreenTable mounted");
});

// 表格配置项
const columns = ref<ColumnProps[]>([
  { prop: "主责部门", label: "部门" },
  { prop: "全年受理总量", label: "受理量", width: "75" },
  { prop: "全年累计解决量", label: "解决量", width: "75" },
  { prop: "全年累计解决率", label: "解决率", width: "90" },
  { prop: "超时工单", label: "超时量", width: "75" },
  { prop: "当月不满意工单", label: "低满量", width: "75" }
]);

// 单击行
const rowClick = (row: any) => {
  emit("clickEvent", "倒三角支撑", []);
  row;
};

// 标注背景颜色
const tableColClassName = ({ column, row }: { column: any; row: any }) => {
  if (column.label === "部门") return "white-text";
  if (column.label === "受理量" || column.label === "解决率") return "success-row";
  return "default-row";
  row;
};

// 表尾合计行计算
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: any[] = [];

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }

    // 这里使用最后一行的数据作为合计值
    const lastRow = data[data.length - 1];
    if (!lastRow) {
      sums[index] = null;
      return;
    }

    const value = lastRow[column.prop];
    sums[index] = value;
  });

  return sums;
};

// 优化列配置生成逻辑
const getColumns = (isYearMode: boolean) => {
  const baseColumns: ColumnProps[] = [
    { prop: "主责部门", label: "部门" },
    { prop: isYearMode ? "全年受理总量" : "当月受理量", label: "受理量", width: "75" },
    { prop: isYearMode ? "全年累计解决量" : "当月解决量", label: "解决量", width: "75" },
    { prop: isYearMode ? "全年累计解决率" : "当月解决率", label: "解决率", width: "90" },
    { prop: "超时工单", label: "超时量", width: "75" },
    { prop: "当月不满意工单", label: "低满量", width: "75" }
  ];
  return baseColumns;
};

// 监听 activeTab 变化
watch(
  () => props.activeTab,
  newTab => {
    const newColumns = getColumns(newTab === "年");
    columns.value = newColumns;
  }
);
</script>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
