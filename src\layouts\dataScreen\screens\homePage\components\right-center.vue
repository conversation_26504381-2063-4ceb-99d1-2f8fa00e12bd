<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="nav-group" @mouseenter="stopAutoplay" @mouseleave="resumeAutoplay">
        <el-button type="primary" :icon="Back" class="nav-btn prev-btn" @click="handlePrev">
          <div class="btn-bg"></div>
        </el-button>
        <div class="chart-title" @click="handleChartClick">
          {{ chartData[currentIndex].title }}
        </div>
        <el-button type="primary" :icon="Right" class="nav-btn next-btn" @click="handleNext">
          <div class="btn-bg"></div>
        </el-button>
      </div>
    </div>
    <div class="chart-content" ref="chartContentRef" @mouseenter="stopAutoplay" @mouseleave="resumeAutoplay">
      <Transition name="fade" mode="out-in">
        <div class="chart-wrapper" :key="currentIndex">
          <StackBarChart
            ref="chartRef"
            :data="formatChartData(chartData[currentIndex])"
            :color="getChartColors(chartData[currentIndex])"
            :is-percentage="false"
            :bar-width="30"
            :show-y-axis="false"
            :show-label="true"
            label-position="inside"
            :label-formatter="value => value.toString()"
            :x-axis-rotate="0"
            :x-axis-width="80"
            :x-axis-font-size="18"
            :x-axis-margin="16"
            :bar-gap="'40%'"
            :grid="{
              top: '10%',
              bottom: '0',
              left: '3%',
              right: '3%',
              containLabel: true
            }"
          />
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import { Right, Back } from "@element-plus/icons-vue";
import StackBarChart from "@/components/ECharts/charts/StackBarChart.vue";

// 类型定义
interface SeriesItem {
  name: string;
  data: number[];
  color: [string, string];
}

interface ChartData {
  title: string;
  xLabel: string[];
  legendData: string[];
  rotate: number;
  series: SeriesItem[];
}

// 图表数据
const chartData: ChartData[] = [
  {
    title: "长沙故障压降情况概览",
    xLabel: ["小区退服压降", "olt压降", "传输压降"],
    legendData: ["目标值", "阶段值"],
    rotate: 0,
    series: [
      {
        name: "目标值",
        data: [709, 1917, 2455],
        color: ["rgba(30, 118, 159, 1)", "rgba(30, 118, 159, 0.3)"]
      },
      {
        name: "阶段值",
        data: [327, 1776, 507],
        color: ["rgba(0, 213, 255, 1)", "rgba(0, 213, 255, 0.3)"]
      }
    ]
  }
];

// 格式化图表数据，适配新的 StackBarChart 组件
const formatChartData = (chart: ChartData) => {
  return {
    xAxis: chart.xLabel.map(label => {
      return label;
    }),
    series: chart.series.map(item => ({
      name: item.name,
      data: item.data,
      formatter: (value: number) => Math.round(value).toString()
    }))
  };
};

// 获取图表颜色
const getChartColors = (chart: ChartData) => {
  return chart.series.map(item => ({
    type: "linear",
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      { offset: 0, color: item.color[0] },
      { offset: 1, color: item.color[1] }
    ]
  }));
};

const currentIndex = ref(0);
const chartRef = ref<InstanceType<typeof StackBarChart> | null>(null);
const chartContentRef = ref<HTMLElement | null>(null);
let autoplayTimer: number | null = null;
const isUserInteracting = ref(false);

// 更新图表尺寸
const updateChartSize = () => {
  if (!chartContentRef.value) return;

  requestAnimationFrame(() => {
    if (chartRef.value?.resize) {
      chartRef.value.resize();
    }
  });
};

// 切换方法
const handlePrev = () => {
  stopAutoplay();
  changeIndex(-1);
};

const handleNext = () => {
  stopAutoplay();
  changeIndex(1);
};

const changeIndex = (step: number) => {
  const newIndex = (currentIndex.value + step + chartData.length) % chartData.length;
  currentIndex.value = newIndex;

  nextTick(() => {
    setTimeout(() => {
      updateChartSize();
    }, 300);
  });
};

// 自动播放控制
const startAutoplay = () => {
  if (isUserInteracting.value) return;

  if (autoplayTimer) {
    clearInterval(autoplayTimer);
  }

  autoplayTimer = window.setInterval(() => {
    if (!document.hidden && !isUserInteracting.value) {
      changeIndex(1);
    }
  }, 4000);
};

const stopAutoplay = () => {
  isUserInteracting.value = true;
  if (autoplayTimer) {
    clearInterval(autoplayTimer);
    autoplayTimer = null;
  }
};

const resumeAutoplay = () => {
  isUserInteracting.value = false;
  startAutoplay();
};

// 点击事件
const handleChartClick = () => {
  stopAutoplay();
  emit("clickEvent", chartData[currentIndex.value].title);
};

// 页面可见性处理函数
const handleVisibilityChange = () => {
  if (document.hidden) {
    stopAutoplay();
  } else {
    resumeAutoplay();
  }
};

// 生命周期钩子
onMounted(() => {
  window.addEventListener("resize", updateChartSize);
  document.addEventListener("visibilitychange", handleVisibilityChange);

  nextTick(() => {
    setTimeout(() => {
      updateChartSize();
      startAutoplay();
    }, 300);
  });
});

onBeforeUnmount(() => {
  stopAutoplay();
  window.removeEventListener("resize", updateChartSize);
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});

const emit = defineEmits<{
  (e: "clickEvent", title: string): void;
}>();
</script>

<style scoped lang="scss">
.chart-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .chart-header {
    position: relative;
    display: flex;
    flex: none;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 0;
    margin-bottom: 10px;
    .nav-group {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 4px;
      background: linear-gradient(
        90deg,
        rgb(18 142 255 / 0%) 0%,
        rgb(18 142 255 / 10%) 25%,
        rgb(18 142 255 / 10%) 75%,
        rgb(18 142 255 / 0%) 100%
      );
      backdrop-filter: blur(4px);
      border-radius: 20px;
      .chart-title {
        position: relative;
        min-width: 200px;
        padding: 0 15px;
        font-size: 20px;
        font-weight: 500;
        color: #b1d8ff;
        text-align: center;
        cursor: pointer;
        transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        &::after {
          position: absolute;
          bottom: -2px;
          left: 50%;
          width: 0;
          height: 2px;
          content: "";
          background: linear-gradient(90deg, transparent, #00d5ff, transparent);
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateX(-50%);
        }
        &:hover {
          color: #00d5ff;
          text-shadow: 0 0 15px rgb(0 213 255 / 40%);
          &::after {
            width: 80%;
            opacity: 1;
          }
        }
      }
      .nav-btn {
        position: absolute;
        top: 50%;
        width: 28px;
        height: 28px;
        padding: 0;
        overflow: hidden;
        color: #b1d8ff;
        background: transparent;
        border: 1px solid rgb(18 142 255 / 30%);
        border-radius: 6px;
        transition: all 0.3s ease;
        transform: translateY(-50%);
        &.prev-btn {
          left: 0;
        }
        &.next-btn {
          right: 0;
        }
        .btn-bg {
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgb(18 142 255 / 10%) 0%, rgb(18 142 255 / 20%) 50%, rgb(18 142 255 / 10%) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        &:hover {
          color: #00d5ff;
          border-color: rgb(18 142 255 / 60%);
          box-shadow: 0 2px 8px rgb(0 213 255 / 20%);
          transform: translateY(calc(-50% - 1px));
          .btn-bg {
            opacity: 1;
          }
        }
        &:active {
          transform: translateY(-50%);
        }
        :deep(.el-icon) {
          z-index: 1;
          font-size: 16px;
        }
      }
    }
  }
  .chart-content {
    position: relative;
    flex: 1;
    min-height: 0;
    padding: 10px;
    overflow: hidden;
    backdrop-filter: blur(4px);
    border: 1px solid rgb(18 142 255 / 10%);
    border-radius: 8px;
    .chart-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      :deep(.echarts-container) {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
