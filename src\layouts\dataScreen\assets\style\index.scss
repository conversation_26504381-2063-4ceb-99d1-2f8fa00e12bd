// 引入公共组件样式文件
@import "./common";
.dataScreen-container {
  width: 100%;
  height: 100%;
  background: #082761;

  // background: url("./components/images/pageBg.png") no-repeat;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: 100% 100%;
  background-size: cover;
  .dataScreen-content {
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 999;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s;
    transform-origin: left top;
    .dataScreen-header {
      position: relative;
      top: 0;
      right: 0;
      left: 0;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 75px;
      background: url("@/views/dataScreen/assets/images/headerBg.png") center no-repeat;
      background-size: 100% 100%;
      .logoImg {
        margin-right: 12px;
      }
      .logo {
        margin-bottom: 10px;
        font-family: AlimamaShuHeiTi-Bold;
        font-size: 38px;
        font-weight: bold;
        line-height: 46px;
        color: #ffffff;
        text-align: center;
        text-shadow: 0 6px 20px rgb(1 48 108 / 62%);
        letter-spacing: 6px;
      }
      .left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 40%;
        padding-left: 20px;
        margin-bottom: 10px;
      }
      .left > img {
        height: 50px;
        margin-right: 8px;
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 40%;
        padding-right: 20px;
        margin-bottom: 10px;
        img {
          height: 50px;
        }
        .el-link {
          display: inline-block;
          padding: 0 16px;
          text-align: center;
          .iconfont {
            display: block;

            // margin-bottom: 4px;
            color: #ffffff;
          }
          &__inner {
            flex-direction: column;
          }
        }
      }
      .el-link {
        font-size: 18px;
        color: #b1d8ff;
      }
      .el-divider--vertical {
        height: 24px;
        margin: 0 10px;
        background: none;
        background-image: radial-gradient(circle at 50% 51%, #5b70aa 0%, rgb(44 54 84 / 0%) 49%);
      }
      .header-lf,
      .header-ri {
        position: relative;
        width: 567px;
        height: 100%;
        background: url("@/views/dataScreen/assets/images/dataScreen-header-left-bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .header-ct {
        position: relative;
        flex: 1;
        height: 100%;
        .header-ct-title {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 82px;
          font-family: YouSheBiaoTiHei;
          font-size: 32px;
          line-height: 78px;
          color: #05e8fe;
          text-align: center;
          letter-spacing: 4px;
          background: url("@/views/dataScreen/assets/images/dataScreen-header-center-bg.png") no-repeat;
          background-size: 100% 100%;
          .header-ct-warning {
            position: absolute;
            bottom: -42px;
            left: 50%;
            width: 622px;
            height: 44px;
            font-family: YouSheBiaoTiHei;
            font-size: 14px;
            line-height: 44px;
            color: #ffffff;
            text-align: center;
            letter-spacing: 1px;
            background: url("@/views/dataScreen/assets/images/dataScreen-header-warn-bg.png") no-repeat;
            background-size: 100% 100%;
            transform: translateX(-50%);
          }
        }
      }
      .header-screening,
      .header-download {
        position: absolute;
        z-index: 9;
        box-sizing: border-box;
        width: 136px;
        height: 42px;
        font-family: YouSheBiaoTiHei;
        font-size: 18px;
        font-weight: 400;
        line-height: 42px;
        color: #29fcff;
        text-align: center;
        cursor: pointer;
        background-size: 100% 100%;
      }
      // .header-screening {
      //   right: 0;
      //   padding-right: 4px;
      //   background: url("@/views/dataScreen/assets/images/dataScreen-header-btn-bg-l.png") no-repeat;
      // }
      // .header-download {
      //   left: 0;
      //   padding-right: 0;
      //   background: url("@/views/dataScreen/assets/images/dataScreen-header-btn-bg-r.png") no-repeat;
      // }
      .header-time {
        position: absolute;
        top: 0;
        right: 14px;
        width: 310px;
        font-family: YouSheBiaoTiHei;
        font-size: 17px;
        font-weight: 400;
        line-height: 38px;
        color: #05e8fe;
        white-space: nowrap;
      }
    }
    .dataScreen-main {
      position: relative;
      box-sizing: border-box;
      display: flex;
      flex: 1;
      width: 100%;
      height: 90%;
      padding: 0;
      .dataScreen-lf {
        z-index: 10;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 25%;
        height: 100%;
        .dataScreen-top,
        .dataScreen-center,
        .dataScreen-bottom {
          position: relative;
          box-sizing: border-box;
          width: 100%;
          padding-top: 10px;
        }
        .dataScreen-top {
          height: 42%;
        }
        // .dataScreen-center {
        //   height: 30%;
        //   background: url("@/views/dataScreen/assets/images/dataScreen-main-lc.png") no-repeat;
        //   background-size: 100% 100%;
        // }
        .dataScreen-bottom {
          height: 25%;
          margin-bottom: 10px;
        }
      }
      .dataScreen-ct {
        position: relative;
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: space-between;
        width: 40%;
        height: 100%;
        margin: 0 20px;
        .dataScreen-map {
          position: relative;
          box-sizing: border-box;
          flex: 1;
          width: 100%;
          height: 65%;
          margin-top: 10px;
          .dataScreen-map-title {
            position: absolute;
            top: 10px;
            left: 0;
            z-index: 99;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            width: 270px;
            height: 35px;
            padding-left: 30px;
            font-size: 20px;
            color: #ffffff;
            letter-spacing: 5px;
            background: url("@/views/dataScreen/assets/images/map-title-bg.png") no-repeat;
            background-size: 100% 100%;
          }
          .dataScreen-alarm {
            height: calc(100% - 35px);
            background: url("@/views/dataScreen/assets/images/bg3.png") no-repeat;
            background-size: 100% 100%;
            .map-item {
              display: flex;
              align-items: center;
              height: 37px;
              cursor: pointer;
              img {
                width: 15px;
                height: 15px;
                margin-top: 3px;
                margin-right: 6px;
              }
              span {
                font-size: 18px;
                color: rgb(255 183 0 / 74.7%);
              }
            }
          }
        }
        .dataScreen-cb {
          position: relative;
          box-sizing: border-box;
          width: 100%;
          height: 30%;
          margin-bottom: 10px;
        }
      }
      .dataScreen-rg {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 25%;
        height: 100%;
        .dataScreen-top,
        .dataScreen-center,
        .dataScreen-bottom {
          position: relative;
          box-sizing: border-box;
          width: 100%;
          padding-top: 10px;
        }
        .dataScreen-top {
          height: 33%;
        }
        .dataScreen-center {
          height: 33%;
        }
        .dataScreen-bottom {
          height: 33%;
          margin-bottom: 10px;
        }
      }
      .dataScreen-main-title {
        position: absolute;
        top: 1px;
        left: 0;
        display: flex;
        flex-direction: column;
        span {
          margin-bottom: 12px;
          font-family: YouSheBiaoTiHei;
          font-size: 18px;
          line-height: 16px;
          color: #ffffff;
          letter-spacing: 1px;
        }
        img {
          width: 68px;
          height: 7px;
        }
      }
      .dataScreen-main-chart {
        width: 100%;
        height: 100%;
      }

      // target
      .tar_content {
        position: absolute;
        width: 100%;
        height: 100%;
        .tar_main {
          justify-content: center;
          height: 95%;
          margin: 5px;
          overflow: hidden;
          .tar_box {
            display: flex;
            flex-wrap: wrap;
            padding: 10px;
            .box_container {
              align-items: center;
              width: calc(33.33% - 20px); /* 计算每个容器的宽度，减去 margin */
              margin: 10px;
              .box-item {
                padding: 15px;
              }
            }
          }
        }
      }
    }
    .dataScreen-main::before {
      position: absolute;
      inset: 0;
      z-index: -1;
      content: "";
      background: url("@/views/dataScreen/assets/images/freamBodyBg.jpg") 50% no-repeat;
      background-size: cover;
    }
    .footer {
      position: relative;
      right: 0;
      bottom: 0;
      left: 0;
      display: inline-block;
      width: 100%;
      height: 28px;
      background: url("@/views/dataScreen/assets/images/freamFooter.png") center no-repeat;
      background-size: 100% 100%;
    }
  }
}

/* 组件公共样式已迁移至 common.scss 文件 */
