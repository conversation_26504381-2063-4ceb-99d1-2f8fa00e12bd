<script setup lang="ts">
import { ref } from 'vue';
import Header from './components/Header/index.vue';
import Footer from './components/Footer/index.vue';
import Container from './components/Container/index.vue';

defineOptions({
  name: 'BigscreenLayout'
});

const title = ref('数据可视化平台');
const enableScale = ref(true);
</script>

<template>
  <Container :design-width="1920" :design-height="1080" :enable-scale="enableScale">
    <!-- 头部导航 -->
    <Header :title="title" @update:scale="val => (enableScale = val)" />
    <!-- 主容器 -->
    <main
      class="relative box-border h-[90%] w-full flex flex-1 p-10 before:absolute before:inset-0 before:z-[-1] before:bg-[url('@/assets/bigscreen/bg.jpg')] before:bg-cover before:bg-center before:bg-no-repeat before:content-['']"
    >
      <RouterView />
    </main>
    <!-- 页脚 -->
    <Footer />
  </Container>
</template>
