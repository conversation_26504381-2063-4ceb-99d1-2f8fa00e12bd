<template>
  <MarketCard :data="marketData" @click="goTo('/newPageV2/marketPage')" />
</template>

<script lang="ts" setup>
// import { ref } from "vue";
import { MarketCard } from "@/views/dataScreen/components";
import { useRouter } from "vue-router";

const router = useRouter();
const marketData = {
  index1: "总人数",
  index2: "新增人数",
  index3: "离职人数",
  index4: "人员流失率",
  value1: "1000",
  value2: "50",
  value3: "20",
  value4: "2.0",
  time1: "截至2023年6月30日",
  time2: "2023年6月",
  time3: "2023年6月",
  time4: "2023年6月",
  status1: "",
  status2: "el-icon-caret-top",
  status3: "el-icon-caret-bottom",
  status4: "el-icon-caret-top",
  rate3: "5",
  rate4: "0.5"
};

const goTo = (url: string) => {
  if (router.currentRoute.value.path !== url) {
    router.push(url);
  }
};
</script>
