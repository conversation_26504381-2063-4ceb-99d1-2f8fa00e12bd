<template>
  <div class="map-ball">
    <div class="map-container">
      <div id="mapChart" class="map_echarts">
        <ECharts :option="option" :resize="true" @click="handleMapClick" />
      </div>
      <div class="map-effects">
        <div class="rotate-circle"></div>
        <div class="pulse-circle"></div>
      </div>
    </div>
    <div class="info-panel">
      <div class="panel-header">区域实时数据</div>
      <div class="panel-content">
        <div class="data-item" v-for="(item, index) in panelData" :key="index">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-value" :class="item.trend">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import echarts, { ECOption } from "@/components/ECharts/config";
import ECharts from "@/components/ECharts/index.vue";
import mapJson from "../../assets/js/cs.json";
import mapBg from "../../assets/images/map.jpeg";
import PointBlue from "../../assets/images/PointBlue.svg";
import PointYellow from "../../assets/images/PointYellow.svg";
import PointGreen from "../../assets/images/PointGreen.svg";
import PointRed from "../../assets/images/PointRed.svg";

/**
 * 地图配置
 */
const MAP_CONFIG = {
  name: "cs",
  scale: 100,
  zoom: 1.18,
  center: ["50%", "50%"]
} as const;

// 注册地图
echarts.registerMap(MAP_CONFIG.name, mapJson as Parameters<typeof echarts.registerMap>[1]);

/**
 * 区域数据
 */
const areaData = [
  { name: "岳麓区", value: [112.78, 28.08, 100, 0.8, "up"], status: "good" },
  { name: "芙蓉区", value: [113.05, 28.16, 90, -0.5, "down"], status: "warning" },
  { name: "天心区", value: [112.95, 28.03, 80, 1.2, "up"], status: "good" },
  { name: "开福区", value: [112.98, 28.33, 70, -0.3, "down"], status: "warning" },
  { name: "雨花区", value: [113.11, 28.01, 60, 0.5, "up"], status: "good" },
  { name: "望城区", value: [112.82, 28.36, 50, -0.2, "down"], status: "warning" },
  { name: "长沙县", value: [113.17, 28.35, 40, 0.3, "up"], status: "good" },
  { name: "宁乡市", value: [112.41, 28.15, 30, -0.4, "down"], status: "warning" },
  { name: "浏阳市", value: [113.63, 28.14, 30, -0.4, "down"], status: "warning" }
];

/**
 * 散点数据
 */
const scatterData = areaData.map(item => ({
  name: item.name,
  value: [...item.value.slice(0, 2), item.status === "good" ? 1 : 0, `${item.value[2]}%`, item.value[4]],
  unit: "%",
  status: item.status
}));

/**
 * 获取点的图标
 * @param {string} status - 状态
 * @returns {string} 图标路径
 */
const getPointIcon = (status: string) => {
  const iconMap = {
    good: PointGreen,
    warning: PointRed,
    normal: PointBlue,
    special: PointYellow
  };
  return `image://${iconMap[status] || PointBlue}`;
};

/**
 * 生成背景图层
 * @returns {Array} 图层配置数组
 */
const generateBackgroundLayers = () => {
  const layers: any[] = [];
  const totalLayers = 24;

  for (let i = 0; i < totalLayers; i++) {
    const opacity = i < 12 ? 1 : i < 18 ? 0.3 : 0.3;
    const shadowIntensity = i < 12 ? 1 : 1.0 - 0.03 * i;

    layers.push({
      map: MAP_CONFIG.name,
      zoom: MAP_CONFIG.zoom,
      layoutCenter: MAP_CONFIG.center,
      layoutSize: `${MAP_CONFIG.scale}%`,
      aspectScale: 1,
      itemStyle: {
        areaColor: "rgba(0,0,0,0)",
        borderWidth: i < 12 ? 2 : 1,
        borderColor: i < 12 ? "#1B9DF1" : "#67BAF1",
        shadowBlur: 2,
        shadowColor: `rgba(2,189,241,${shadowIntensity})`,
        shadowOffsetY: 2 * i,
        opacity
      },
      silent: true,
      zlevel: 0
    });
  }

  return layers;
};

/**
 * 地图点击事件
 * @param {any} params - 点击参数
 */
const handleMapClick = (params: any) => {
  console.log(params);
  if (params.data) {
    const clickData = {
      name: params.data.name,
      value: params.data.value[2],
      trend: params.data.value[4],
      status: params.data.status
    };
    emit("map-click", clickData);
  }
};

/**
 * ECharts配置项
 */
const option = ref<ECOption | any>({
  tooltip: {
    show: true,
    trigger: "item",
    backgroundColor: "transparent",
    borderWidth: 0,
    confine: true,
    enterable: false,
    position: function (point: number[], params: any, dom: HTMLElement, rect: any, size: any) {
      const [x, y] = point;
      const { contentSize } = size;
      const { viewSize } = size;

      let posX = x + 20;
      let posY = y + 20;

      if (posX + contentSize[0] > viewSize[0]) {
        posX = x - contentSize[0] - 20;
      }

      if (posY + contentSize[1] > viewSize[1]) {
        posY = y - contentSize[1] - 20;
      }

      posX = Math.max(20, posX);
      posY = Math.max(20, posY);

      return [posX, posY];
    },
    formatter: (params: any) => {
      // 只处理地图系列的数据
      if (params.seriesType !== "map") return "";

      const data = scatterData.find(item => item.name === params.name);
      if (!data) return "";

      const value = data.value[3];
      const trend = data.value[4];
      const trendIcon = trend === "up" ? '<span class="trend trend-up">↑</span>' : '<span class="trend trend-down">↓</span>';

      return `
        <div class="custom-tooltip">
          <div class="tooltip-header">
            <span class="area-name">${params.name}</span>
          </div>
          <div class="tooltip-body">
            <div class="data-row">
              <span class="label">环比：</span>
              <span class="value">${value}</span>
              ${trendIcon}
            </div>
            <div class="data-row">
              <span class="label">排名：</span>
              <span class="value">${data.value[2]}</span>
            </div>
          </div>
        </div>
      `;
    }
  },
  geo: [
    {
      map: MAP_CONFIG.name,
      zoom: MAP_CONFIG.zoom,
      layoutCenter: MAP_CONFIG.center,
      layoutSize: `${MAP_CONFIG.scale}%`,
      aspectScale: 1,
      itemStyle: {
        areaColor: "rgba(0,0,0,0)",
        borderWidth: 0.5,
        borderColor: "#87CDFF"
      },
      emphasis: {
        disabled: true
      },
      select: {
        disabled: true
      },
      zlevel: 24
    },
    ...generateBackgroundLayers(),
    // 背景纹理
    {
      map: MAP_CONFIG.name,
      zoom: MAP_CONFIG.zoom,
      layoutCenter: MAP_CONFIG.center,
      layoutSize: `${MAP_CONFIG.scale}%`,
      aspectScale: 1,
      itemStyle: {
        areaColor: {
          image: mapBg,
          repeat: "repeat"
        }
      },
      silent: true,
      zlevel: 2
    }
  ],
  series: [
    {
      type: "map",
      map: MAP_CONFIG.name,
      geoIndex: 0,
      data: areaData,
      zlevel: 3,
      emphasis: {
        disabled: true
      },
      select: {
        disabled: true
      },
      tooltip: {
        show: true
      }
    },
    {
      name: "区域标记",
      type: "scatter",
      coordinateSystem: "geo",
      geoIndex: 0,
      symbolOffset: [10, -11],
      symbol: value => getPointIcon(value[2] === 1 ? "good" : "warning"),
      symbolSize: [30, 22],
      label: {
        show: true,
        formatter: (param: any) => {
          return `{value|${param.value[3]}}\n{name|${param.name}}`;
        },
        rich: {
          value: {
            fontFamily: "futura",
            fontSize: 20,
            fontWeight: "bold",
            color: "#00FF92",
            padding: [0, 0, 30, 0]
          },
          name: {
            color: "#ffffff",
            fontSize: 20,
            fontWeight: "bold",
            fontFamily: "YousheBiaoriHei"
          }
        }
      },
      itemStyle: {
        color: "rgba(0,0,0,0)"
      },
      data: scatterData,
      zlevel: 24,
      tooltip: {
        show: false
      }
    }
  ]
});

/**
 * 发射事件
 */
const emit = defineEmits<{
  (e: "map-click", data: any): void;
}>();

/**
 * 添加面板数据
 */
const panelData = ref([
  { label: "总体评分", value: "96.5", trend: "up" },
  { label: "网络质量", value: "98.2", trend: "up" },
  { label: "用户满意度", value: "94.8", trend: "down" },
  { label: "覆盖范围", value: "92.3", trend: "up" }
]);
</script>

<style lang="scss" scoped>
.map-ball {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  .map-container {
    position: relative;
    width: 100%;
    height: 100%;
    .map_echarts {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
      width: 100%;
      height: 100%;
      transform: scale(1.2);
    }
  }
  .map-effects {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 0;
    width: 100%;
    height: 100%;
    transform: translate(-50%, -50%) rotateX(45deg) scale(1.2);
    .rotate-circle {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 908px;
      height: 908px;
      margin: -454px 0 0 -454px;
      background: url("../../assets/images/Chart-BG-Disk.svg") center no-repeat;
      background-size: contain;
      animation: rotate 20s linear infinite;
    }
    .pulse-circle {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 300px;
      height: 300px;
      margin: -150px 0 0 -150px;
      background: radial-gradient(circle, rgb(0 213 255 / 20%) 0%, transparent 70%);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: pulse 2s ease-in-out infinite;
    }
  }
  .info-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 20;
    width: 200px;
    overflow: hidden;
    background: rgb(2 50 101 / 80%);
    backdrop-filter: blur(4px);
    border: 1px solid rgb(0 213 255 / 30%);
    border-radius: 4px;
    .panel-header {
      padding: 8px 12px;
      font-size: 16px;
      color: #ffffff;
      background: linear-gradient(90deg, rgb(0 213 255 / 20%), transparent);
    }
    .panel-content {
      padding: 12px;
      .data-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        .item-label {
          color: #b1d8ff;
        }
        .item-value {
          font-family: futura;
          font-weight: bold;
          &.up {
            color: #00ff92;
            &::after {
              margin-left: 4px;
              content: "↑";
            }
          }
          &.down {
            color: #ff6161;
            &::after {
              margin-left: 4px;
              content: "↓";
            }
          }
        }
      }
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-40deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(40deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
}
:deep(.custom-tooltip) {
  position: relative;
  z-index: 100;
  min-width: 150px;
  padding: 12px;
  pointer-events: none;
  background: linear-gradient(135deg, rgb(2 50 101 / 95%) 0%, rgb(2 50 101 / 85%) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgb(0 213 255 / 20%);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
  .tooltip-header {
    padding-bottom: 8px;
    margin-bottom: 8px;
    border-bottom: 1px solid rgb(0 213 255 / 10%);
    .area-name {
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 0 8px rgb(0 213 255 / 30%);
    }
  }
  .tooltip-body {
    .data-row {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        margin-right: 8px;
        color: rgb(177 216 255 / 80%);
      }
      .value {
        font-family: futura;
        font-weight: bold;
        color: #00d5ff;
      }
      .trend {
        margin-left: 6px;
        font-weight: bold;
        &.trend-up {
          color: #00ff92;
        }
        &.trend-down {
          color: #ff6161;
        }
      }
    }
  }
}

@keyframes scan-beam {
  0% {
    opacity: 0;
    transform: rotate(0deg);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: rotate(360deg);
  }
}

@keyframes flow-right {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateX(100%);
  }
}
.flow-line {
  animation: flow-right 3s linear infinite;
}

// 四角装饰
.corner-decoration {
  position: absolute;
  width: 30px;
  height: 30px;
  border: 2px solid #00d5ff;
  opacity: 0.6;
  &::before,
  &::after {
    position: absolute;
    content: "";
    background: #00d5ff;
  }
  &.top-left {
    top: 10px;
    left: 10px;
    border-right: none;
    border-bottom: none;
  }
  &.top-right {
    top: 10px;
    right: 10px;
    border-bottom: none;
    border-left: none;
  }
  &.bottom-left {
    bottom: 10px;
    left: 10px;
    border-top: none;
    border-right: none;
  }
  &.bottom-right {
    right: 10px;
    bottom: 10px;
    border-top: none;
    border-left: none;
  }
}

// 扫描光束
.scan-beam {
  position: absolute;
  top: 0;
  left: 50%;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, transparent, #00d5ff, #00d5ff, transparent);
  transform-origin: top;
  animation: scanBeam 4s linear infinite;
}

// 添加点击效果
.map_echarts {
  :deep(.map) {
    cursor: pointer;
    path {
      transition: all 0.3s ease;
      &:active {
        transform: scale(0.98);
      }
    }
  }
}
</style>
