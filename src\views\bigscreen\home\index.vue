<script setup lang="ts">
// 逻辑后续补充
</script>

<template>
  <!-- 顶部导航栏 -->
  <header class="h-16 w-full flex items-center bg-[#14263c] px-8 text-white shadow-lg">
    <div class="text-2xl font-bold">数据大屏</div>
    <!-- 可加右侧操作按钮等 -->
  </header>

  <!-- 主体内容区 -->
  <main class="w-full flex flex-1 gap-4 bg-[#0a1d2b] px-6 py-4">
    <!-- 左侧三个容器 -->
    <section class="min-w-60 w-1/5 flex flex-col gap-4">
      <div class="h-40 rounded-lg bg-[#193a5a] p-4">左1</div>
      <div class="h-40 rounded-lg bg-[#193a5a] p-4">左2</div>
      <div class="flex-1 rounded-lg bg-[#193a5a] p-4">左3</div>
    </section>

    <!-- 中间区域 -->
    <section class="min-w-0 flex flex-col flex-1 gap-4">
      <div class="h-72 flex items-center justify-center rounded-lg bg-[#193a5a] p-4">
        <!-- 地图区域 -->
        <span>地图区域</span>
      </div>
      <div class="mt-2 flex-1 rounded-lg bg-[#193a5a] p-4">中下容器</div>
    </section>

    <!-- 右侧三个容器 -->
    <section class="min-w-60 w-1/5 flex flex-col gap-4">
      <div class="h-40 rounded-lg bg-[#193a5a] p-4">右1</div>
      <div class="h-40 rounded-lg bg-[#193a5a] p-4">右2</div>
      <div class="flex-1 rounded-lg bg-[#193a5a] p-4">右3</div>
    </section>
  </main>
</template>

<style scoped></style>
