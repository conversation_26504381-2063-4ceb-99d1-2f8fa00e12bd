<script setup lang="ts">
import { ref } from 'vue';
import { useBigscreenTheme } from '@/layouts/bigscreen-layout/theme/index';
import AiDialog from '@/layouts/bigscreen-layout/components/AiDialog/index.vue';
import SettingsDialog from '@/layouts/bigscreen-layout/components/SettingsDialog/index.vue';

defineOptions({
  name: 'BigscreenHome'
});

const { bigscreenTheme, themeStore } = useBigscreenTheme();

// 对话框状态
const aiDialogVisible = ref(false);
const settingsDialogVisible = ref(false);

// 示例数据
const leftData = [
  { title: '实时访问量', value: '12,580', unit: '人次', trend: '+12.5%' },
  { title: '在线用户数', value: '3,420', unit: '人', trend: '+8.2%' },
  { title: '系统负载', value: '68.5', unit: '%', trend: '-2.1%' }
];

const rightData = [
  { title: '今日订单', value: '8,960', unit: '单', trend: '+15.3%' },
  { title: '销售额', value: '156.8', unit: '万', trend: '+22.1%' },
  { title: '转化率', value: '12.8', unit: '%', trend: '+5.6%' }
];

const openAiDialog = () => {
  aiDialogVisible.value = true;
};

const openSettingsDialog = () => {
  settingsDialogVisible.value = true;
};
</script>

<template>
  <div class="bigscreen-home h-full w-full">
    <!-- 顶部导航栏 -->
    <header
      class="h-16 w-full flex items-center justify-between px-8 shadow-lg"
      :style="{
        backgroundColor: bigscreenTheme.colors.cardBg,
        color: bigscreenTheme.colors.textColor,
        borderBottom: `1px solid ${bigscreenTheme.colors.borderColor}`
      }"
    >
      <div class="text-2xl font-bold">数据大屏展示</div>
      <div class="flex gap-3">
        <NButton
          type="primary"
          size="small"
          class="bigscreen-btn"
          :style="{
            backgroundColor: bigscreenTheme.colors.buttonBg,
            borderColor: bigscreenTheme.colors.borderColor,
            color: bigscreenTheme.colors.textColor
          }"
          @click="openAiDialog"
        >
          <template #icon>
            <NIcon>
              <SvgIcon icon="mdi:robot" />
            </NIcon>
          </template>
          AI助手
        </NButton>
        <NButton
          type="primary"
          size="small"
          class="bigscreen-btn"
          :style="{
            backgroundColor: bigscreenTheme.colors.buttonBg,
            borderColor: bigscreenTheme.colors.borderColor,
            color: bigscreenTheme.colors.textColor
          }"
          @click="openSettingsDialog"
        >
          <template #icon>
            <NIcon>
              <SvgIcon icon="mdi:cog" />
            </NIcon>
          </template>
          设置
        </NButton>
        <NButton
          type="primary"
          size="small"
          class="bigscreen-btn"
          :style="{
            backgroundColor: bigscreenTheme.colors.successColor,
            borderColor: bigscreenTheme.colors.borderColor,
            color: bigscreenTheme.colors.textColor
          }"
          @click="themeStore.toggleThemeScheme"
        >
          <template #icon>
            <NIcon>
              <SvgIcon :icon="themeStore.darkMode ? 'mdi:weather-sunny' : 'mdi:weather-night'" />
            </NIcon>
          </template>
          {{ themeStore.darkMode ? '亮色' : '暗色' }}
        </NButton>
      </div>
    </header>

    <!-- 主体内容区 -->
    <main class="w-full flex flex-1 gap-4 p-6">
      <!-- 左侧三个容器 -->
      <section class="min-w-60 w-1/5 flex flex-col gap-4">
        <div
          v-for="(item, index) in leftData"
          :key="index"
          class="bigscreen-card rounded-lg p-4 transition-all hover:scale-105"
          :class="{ 'flex-1': index === 2 }"
          :style="{
            backgroundColor: bigscreenTheme.colors.cardBg,
            border: `1px solid ${bigscreenTheme.colors.borderColor}`,
            boxShadow: bigscreenTheme.shadows.card
          }"
        >
          <h3 class="mb-2 text-sm font-medium" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
            {{ item.title }}
          </h3>
          <div class="flex items-end justify-between">
            <div>
              <span class="text-2xl font-bold" :style="{ color: bigscreenTheme.colors.textColor }">
                {{ item.value }}
              </span>
              <span class="ml-1 text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
                {{ item.unit }}
              </span>
            </div>
            <span
              class="text-xs"
              :style="{
                color: item.trend.startsWith('+')
                  ? bigscreenTheme.colors.successColor
                  : bigscreenTheme.colors.errorColor
              }"
            >
              {{ item.trend }}
            </span>
          </div>
        </div>
      </section>

      <!-- 中间区域 -->
      <section class="min-w-0 flex flex-col flex-1 gap-4">
        <div
          class="h-72 flex items-center justify-center rounded-lg p-4 bigscreen-card"
          :style="{
            backgroundColor: bigscreenTheme.colors.cardBg,
            border: `1px solid ${bigscreenTheme.colors.borderColor}`,
            boxShadow: bigscreenTheme.shadows.card
          }"
        >
          <!-- 地图区域 -->
          <div class="text-center">
            <NIcon size="48" :style="{ color: bigscreenTheme.colors.buttonBg }">
              <SvgIcon icon="mdi:map" />
            </NIcon>
            <p class="mt-2" :style="{ color: bigscreenTheme.colors.textColor }">地图区域</p>
            <p class="text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
              这里可以放置地图组件或其他可视化图表
            </p>
          </div>
        </div>
        <div
          class="mt-2 flex-1 rounded-lg p-4 bigscreen-card"
          :style="{
            backgroundColor: bigscreenTheme.colors.cardBg,
            border: `1px solid ${bigscreenTheme.colors.borderColor}`,
            boxShadow: bigscreenTheme.shadows.card
          }"
        >
          <div class="text-center">
            <NIcon size="48" :style="{ color: bigscreenTheme.colors.infoColor }">
              <SvgIcon icon="mdi:chart-line" />
            </NIcon>
            <p class="mt-2" :style="{ color: bigscreenTheme.colors.textColor }">数据图表区域</p>
            <p class="text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
              这里可以放置各种数据图表和统计信息
            </p>
          </div>
        </div>
      </section>

      <!-- 右侧三个容器 -->
      <section class="min-w-60 w-1/5 flex flex-col gap-4">
        <div
          v-for="(item, index) in rightData"
          :key="index"
          class="bigscreen-card rounded-lg p-4 transition-all hover:scale-105"
          :class="{ 'flex-1': index === 2 }"
          :style="{
            backgroundColor: bigscreenTheme.colors.cardBg,
            border: `1px solid ${bigscreenTheme.colors.borderColor}`,
            boxShadow: bigscreenTheme.shadows.card
          }"
        >
          <h3 class="mb-2 text-sm font-medium" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
            {{ item.title }}
          </h3>
          <div class="flex items-end justify-between">
            <div>
              <span class="text-2xl font-bold" :style="{ color: bigscreenTheme.colors.textColor }">
                {{ item.value }}
              </span>
              <span class="ml-1 text-sm" :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
                {{ item.unit }}
              </span>
            </div>
            <span
              class="text-xs"
              :style="{
                color: item.trend.startsWith('+')
                  ? bigscreenTheme.colors.successColor
                  : bigscreenTheme.colors.errorColor
              }"
            >
              {{ item.trend }}
            </span>
          </div>
        </div>
      </section>
    </main>

    <!-- 对话框组件 -->
    <AiDialog v-model:visible="aiDialogVisible" />
    <SettingsDialog v-model:visible="settingsDialogVisible" />
  </div>
</template>

<style scoped>
.bigscreen-home {
  background: v-bind('bigscreenTheme.gradients.background');
  min-height: 100vh;
}

.bigscreen-card {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.bigscreen-card:hover {
  transform: translateY(-2px);
  box-shadow: v-bind('bigscreenTheme.shadows.dialog');
}

.bigscreen-btn {
  transition: all 0.3s ease;
  box-shadow: v-bind('bigscreenTheme.shadows.button');
}

.bigscreen-btn:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}
</style>
