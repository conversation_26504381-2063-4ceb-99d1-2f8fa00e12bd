<template>
  <LayoutContainer :title="title" :design-width="1920" :design-height="1080">
    <router-view></router-view>
  </LayoutContainer>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { LayoutContainer } from "./layout";
import "./assets/style/index.scss";

/**
 * 大屏标题
 * @type {import('vue').Ref<string>}
 */
const title = ref("网络中台");
</script>

<style scoped>
:deep(.router-view) {
  display: flex;
  width: 100%;
  height: 100%;
}
</style>
