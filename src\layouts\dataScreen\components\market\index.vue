<template>
  <div class="market-card">
    <div class="market-card-header mb10">{{ data.index1 }}</div>
    <div class="market-card-left">
      <div class="market-card-num">{{ data.value1 }}</div>
      <div class="market-card-text">{{ data.time1 }}</div>
    </div>
    <div class="market-card-right">
      <div class="market-card-right-item">
        <div>{{ data.index2 }}</div>
        <div class="mb8">
          <span>{{ data.value2 }}</span>
          <i class="ml5" :class="data.status2"></i>
          <span v-if="data.rate2" class="ml3 small-text">({{ data.rate2 }}%)</span>
        </div>
        <div class="market-card-time">{{ data.time2 }}</div>
      </div>
      <div class="market-card-right-item">
        <div>{{ data.index3 }}</div>
        <div class="mb8">
          <span>{{ data.value3 }}</span>
          <i class="ml5" :class="data.status3"></i>
          <span class="ml3 small-text" v-if="data.rate3">({{ data.rate3 }}%)</span>
        </div>
        <div class="market-card-time">{{ data.time3 }}</div>
      </div>
      <div class="market-card-right-item">
        <div>{{ data.index4 }}</div>
        <div class="mb8">
          <span>{{ data.value4 }}</span>
          <i class="ml5" :class="data.status4"></i>
          <span class="ml3 small-text" v-if="data.rate4">({{ data.rate4 }}%)</span>
        </div>
        <div class="market-card-time">{{ data.time4 }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * @typedef {Object} MarketData
 * @property {string} index1 - 标题1
 * @property {string} index2 - 标题2
 * @property {string} index3 - 标题3
 * @property {string} index4 - 标题4
 * @property {string} value1 - 值1
 * @property {string} value2 - 值2
 * @property {string} value3 - 值3
 * @property {string} value4 - 值4
 * @property {string} time1 - 时间1
 * @property {string} time2 - 时间2
 * @property {string} time3 - 时间3
 * @property {string} time4 - 时间4
 * @property {string} status1 - 状态1
 * @property {string} status2 - 状态2
 * @property {string} status3 - 状态3
 * @property {string} status4 - 状态4
 * @property {string} [rate1] - 比率1
 * @property {string} [rate2] - 比率2
 * @property {string} [rate3] - 比率3
 * @property {string} [rate4] - 比率4
 */

/**
 * 组件属性接口
 */
interface Props {
  /**
   * 数据对象
   */
  data: {
    index1: string;
    index2: string;
    index3: string;
    index4: string;
    value1: string;
    value2: string;
    value3: string;
    value4: string;
    time1: string;
    time2: string;
    time3: string;
    time4: string;
    status1: string;
    status2: string;
    status3: string;
    status4: string;
    rate1?: string;
    rate2?: string;
    rate3?: string;
    rate4?: string;
  };
}

defineProps<Props>();
</script>

<style lang="scss" scoped>
// 样式已移至公共样式文件
</style>
