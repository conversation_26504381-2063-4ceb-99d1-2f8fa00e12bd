/* 组件公共样式 */
// 标准卡片样式
.normal-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: rgb(140 189 255 / 4%);
  backdrop-filter: blur(8px);
  .normal-card-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    padding: 0 15px;
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      content: "";
      background: url("../images/panelHeader.png") no-repeat;
      background-size: 100% 100%;
    }
    .normal-card-title {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      padding-left: 5px;
      font-family: YouSheBiaoTiHei;
      font-size: 30px;
      color: #ffffff;
      text-shadow: 0 0 4px #0066ff;

      // cursor: pointer;
      .icon {
        position: relative;
        box-sizing: border-box;
        display: inline-block;
        width: 15px;
        height: 15px;
        margin-right: 15px;
        border: 2px solid #14b4ff;
        &::before {
          position: absolute;
          top: 3px;
          left: 3px;
          display: inline-block;
          width: 5px;
          height: 5px;
          content: "";
          background: #f9b93b;
          box-shadow:
            0 0 8px 1px #0091f8,
            0 0 4px 0 rgb(33 121 195 / 88%);
        }
      }
    }
  }
  .normal-card-main {
    flex: 1;

    // padding: 10px;
    overflow: hidden;
  }
}

// 市场卡片样式
.market-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: url("../images/marketBg.png") no-repeat;
  background-size: 100% 100%;
  .market-card-header {
    width: 80%;
    padding-left: 50px;
    margin-top: 36px;
    font-size: 14px;
    color: #ffffff;
  }
  .market-card-left {
    width: 80%;
    padding-left: 50px;
    .market-card-num {
      font-size: 60px;
      font-weight: 400;
      line-height: 40px;
      color: #1df7f7;
      letter-spacing: -2px;
    }
    .market-card-text {
      padding-top: 10px;
      font-size: 14px;
      color: #ffffff;
    }
  }
  .market-card-right {
    display: flex;
    width: 100%;
    padding: 20px 50px;
    .market-card-right-item {
      width: 100%;
      font-size: 14px;
      line-height: 20px;
      color: #ffffff;
      .small-text {
        font-size: 12px;
      }
      .market-card-time {
        font-size: 12px;
        color: rgb(255 255 255 / 60%);
      }
    }
  }
}

// 通用按钮样式
.button-group {
  display: flex;
  gap: 8px;
}
.btnChange {
  background: linear-gradient(
    90deg,
    rgb(18 142 255 / 0%) 0%,
    rgb(18 142 255 / 10%) 25%,
    rgb(18 142 255 / 10%) 75%,
    rgb(18 142 255 / 0%) 100%
  );
  backdrop-filter: blur(4px);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgb(0 213 255 / 0%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  &:hover {
    background: linear-gradient(90deg, rgb(0 213 255 / 10%) 0%, rgb(0 213 255 / 20%) 50%, rgb(0 213 255 / 10%) 100%);
    box-shadow: 0 4px 12px rgb(0 213 255 / 15%);
    transform: translateY(-1px);
  }
  &:active {
    box-shadow: 0 2px 8px rgb(0 213 255 / 0%);
    transform: translateY(0);
  }
}
.activeBtn {
  background: linear-gradient(90deg, rgb(0 213 255 / 20%) 0%, rgb(0 213 255 / 40%) 50%, rgb(0 213 255 / 20%) 100%);
  box-shadow: 0 4px 12px rgb(0 213 255 / 25%);
}

// 对话框样式
.dt-dialog {
  .el-dialog__body {
    padding: 10px;
  }
  .el-dialog__header {
    padding: 15px 20px;
    margin-right: 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
}

// 通用容器高度样式
.dataScreen-center {
  .el-card__header {
    position: relative;
    z-index: 1;
    height: 40px;
    padding: 0;
    line-height: 40px;
  }
  .el-card__body {
    position: relative;
    height: calc(100% - 40px);
    padding: 10px;
    overflow: hidden;
  }
}

// 防止内容溢出
.el-card {
  overflow: hidden;
  .el-card__body {
    overflow: hidden;
  }
}

// 箭头图标样式
.arrowUp {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 12px;
  height: 15px;
  color: #00ff88;
  background: url("../images/up.svg") no-repeat;
}
.arrowDown {
  position: relative;
  top: 4px;
  display: inline-block;
  width: 12px;
  height: 15px;
  color: #ff4d4f;
  background: url("../images/down.svg") no-repeat;
}

// 导航组样式
.nav-group {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 4px;
  background: linear-gradient(
    90deg,
    rgb(18 142 255 / 0%) 0%,
    rgb(18 142 255 / 10%) 25%,
    rgb(18 142 255 / 10%) 75%,
    rgb(18 142 255 / 0%) 100%
  );
  backdrop-filter: blur(4px);
  border-radius: 20px;
}

// 角落装饰元素
.corner-decoration {
  position: absolute;
  width: 6px;
  height: 6px;
  border: 1px solid rgb(0 213 255 / 30%);
  opacity: 0;
  transition: all 0.3s;
  &.top-left {
    top: 4px;
    left: 4px;
    border-right: none;
    border-bottom: none;
  }
  &.top-right {
    top: 4px;
    right: 4px;
    border-bottom: none;
    border-left: none;
  }
  &.bottom-left {
    bottom: 4px;
    left: 4px;
    border-top: none;
    border-right: none;
  }
  &.bottom-right {
    right: 4px;
    bottom: 4px;
    border-top: none;
    border-left: none;
  }
}

// 悬停时的角落装饰动画
.hasHover {
  &:hover {
    .corner-decoration {
      opacity: 1;
      &.top-left {
        transform: translate(-2px, -2px);
      }
      &.top-right {
        transform: translate(2px, -2px);
      }
      &.bottom-left {
        transform: translate(-2px, 2px);
      }
      &.bottom-right {
        transform: translate(2px, 2px);
      }
    }
  }
}

// 提示框自定义样式
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(151 191 229), rgb(129 177 229));
}
.el-popper.is-customized .el-popper__arrow::before {
  right: 0;
  background: linear-gradient(45deg, #8dc1e6, #89aae6);
}

// 数据网格专用提示框
:deep(.data-tooltip) {
  background: linear-gradient(135deg, rgb(0 80 150 / 90%) 0%, rgb(20 95 135 / 95%) 100%);
  border-color: rgb(0 213 255 / 50%);
  box-shadow:
    0 8px 24px rgb(0 0 0 / 40%),
    0 0 15px rgb(0 213 255 / 25%) inset;
  .el-tooltip__content {
    padding: 8px 12px;
    font-family: Orbitron, sans-serif;
    font-size: 15px;
    color: #00d5ff;
    text-shadow: 0 0 8px rgb(0 213 255 / 50%);
    letter-spacing: 0.5px;
  }
  .el-popper__arrow::before {
    background: rgb(0 213 255 / 50%);
    border-color: rgb(0 213 255 / 50%);
  }
}
