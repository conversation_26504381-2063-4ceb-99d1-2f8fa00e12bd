<template>
  <div class="chart-container">
    <div class="chart-header">
      <div class="nav-group" @mouseenter="stopAutoplay" @mouseleave="resumeAutoplay">
        <el-button type="primary" :icon="Back" class="nav-btn prev-btn" @click="handlePrev">
          <div class="btn-bg"></div>
        </el-button>
        <div class="chart-title" @click="handleChartClick">
          {{ params[currentIndex].title }}
        </div>
        <el-button type="primary" :icon="Right" class="nav-btn next-btn" @click="handleNext">
          <div class="btn-bg"></div>
        </el-button>
      </div>
    </div>
    <div class="chart-content" ref="chartContentRef" @mouseenter="stopAutoplay" @mouseleave="resumeAutoplay">
      <Transition name="fade" mode="out-in">
        <div class="chart-wrapper" :key="currentIndex" @mouseenter="stopAutoplay" @mouseleave="resumeAutoplay">
          <BarLineComboChart
            ref="chartRef"
            :data="formatChartData(params[currentIndex])"
            :color="getChartColors(params[currentIndex])"
            :show-y-axis="false"
            :show-label="true"
            label-position="top"
            :x-axis-rotate="params[currentIndex].rotate"
            :x-axis-width="60"
            :x-axis-font-size="16"
            :x-axis-margin="16"
            :grid="{
              top: '30%',
              bottom: '0',
              left: '5%',
              right: '3%',
              containLabel: true
            }"
          />
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue";
import { Right, Back } from "@element-plus/icons-vue";
import BarLineComboChart from "@/components/ECharts/charts/BarLineComboChart.vue";

// 定义类型接口
interface ChartDataItem {
  name: string;
  type: "bar" | "line";
  data: number[];
  color: [string, string];
  isPercentage: boolean;
}

interface ChartData {
  title: string;
  xLabel: string[];
  legendData: string[];
  rotate: number;
  data: ChartDataItem[];
}

// 格式化图表数据
const formatChartData = (chart: ChartData) => {
  const barData = chart.data.find(item => item.type === "bar") as ChartDataItem;
  const lineData = chart.data.find(item => item.type === "line") as ChartDataItem;

  if (!barData || !lineData) {
    throw new Error("数据格式错误：需要至少一个 bar 类型和一个 line 类型的数据");
  }

  return {
    xAxis: chart.xLabel,
    bar: {
      name: barData.name,
      data: barData.data,
      isPercentage: barData.isPercentage
    },
    line: {
      name: lineData.name,
      data: lineData.data,
      isPercentage: lineData.isPercentage
    }
  };
};

// 获取图表颜色
const getChartColors = (chart: ChartData) => {
  return chart.data
    .filter(item => item.type === "bar" || item.type === "line")
    .map(item => ({
      type: "linear",
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        { offset: 0, color: item.color[0] },
        { offset: 1, color: item.color[1] }
      ]
    }));
};

// 图表数据
const params: ChartData[] = [
  {
    title: "机房电费异常核查",
    xLabel: ["天心区", "开福区", "芙蓉区", "雨花区", "岳麓区", "长沙县", "望城区", "浏阳市", "宁乡市", "合计"],
    legendData: ["10月后评估任务量", "核查任务量", "核查进度"],
    rotate: 45,
    data: [
      {
        name: "10月后评估任务量",
        type: "bar",
        data: [17, 21, 9, 36, 26, 44, 39, 46, 34, 271],
        color: ["rgba(30, 118, 159, 1)", "rgba(30, 118, 159, 0.3)"],
        isPercentage: false
      },
      {
        name: "核查进度",
        type: "line",
        data: [101.64, 157.83, 110.0, 99.45, 106.24, 106.11, 106.76, 114.63, 118.43, 112.0],
        color: ["rgba(128,255,212,1)", "rgba(128,255,212,0.5)"],
        isPercentage: true
      },
      {
        name: "核查任务量",
        type: "bar",
        data: [7, 6, 5, 10, 6, 18, 19, 10, 19, 100],
        color: ["rgba(0, 213, 255, 1)", "rgba(0, 213, 255, 0.3)"],
        isPercentage: false
      }
    ]
  },
  {
    title: "铁塔单站服务费压降",
    xLabel: ["芙蓉区", "雨花区", "天心区", "开福区", "岳麓区", "长沙县", "望城区", "浏阳市", "宁乡市", "长沙"],
    legendData: ["截止本月单站服务费平均年化值", "单站平均年化值降幅"],
    rotate: 45,
    data: [
      {
        name: "截止本月单站服务费平均年化值",
        type: "bar",
        data: [2117.77, 2142.69, 2337.22, 2104.12, 2073.4, 1973.96, 1992.16, 1990.9, 2043.38, 2060.84],
        color: ["rgba(30, 118, 159, 1)", "rgba(30, 118, 159, 0.3)"],
        isPercentage: false
      },
      {
        name: "单站平均年化值降幅",
        type: "line",
        data: [5.98, 8.34, 5.66, 6.0, 7.67, 6.96, 8.21, 6.37, 11.84, 7.91],
        color: ["rgba(128,255,212,1)", "rgba(128,255,212,0.5)"],
        isPercentage: true
      }
    ]
  },
  {
    title: "长沙2G网络与客户升级及2G资产处置专题",
    xLabel: ["2G小区退网", "有2无4覆盖", "4/5G打底网建设", "设备拆除报废移交率", "设备再利用比例"],
    legendData: ["得分", "当前进度", "失分"],
    rotate: 0,
    data: [
      {
        name: "得分",
        type: "bar",
        data: [16.74, 3.5, 2.32, 4.79, 0.72],
        color: ["rgba(30, 118, 159, 1)", "rgba(30, 118, 159, 0.3)"],
        isPercentage: false
      },
      {
        name: "当前进度",
        type: "line",
        data: [89.7, 100, 40.9, 44.9, 38.8],
        color: ["rgba(0, 213, 255, 1)", "rgba(0, 213, 255, 0.3)"],
        isPercentage: true
      },
      {
        name: "失分",
        type: "bar",
        data: [7.76, 0, 1.18, 1.21, 0.28],
        color: ["rgba(30, 118, 159, 1)", "rgba(30, 118, 159, 0.3)"],
        isPercentage: false
      }
    ]
  },
  {
    title: "9月份千兆质差小区整改情况",
    xLabel: ["芙蓉区", "开福区", "浏阳市", "宁乡县", "天心区", "望城区", "雨花区", "岳麓区", "长沙县", "总计"],
    legendData: ["质差小区总量", "总计整改", "整改完成率"],
    rotate: 45,
    data: [
      {
        name: "质差小区总量",
        type: "bar",
        data: [7, 14, 2, 7, 10, 11, 20, 24, 7, 102],
        color: ["rgba(30, 118, 159, 1)", "rgba(30, 118, 159, 0.3)"],
        isPercentage: false
      },
      {
        name: "整改完成率",
        type: "line",
        data: [100.0, 78.57, 100.0, 28.57, 60.0, 100.0, 90.0, 100.0, 100.0, 86.27],
        color: ["rgba(128,255,212,1)", "rgba(128,255,212,0.5)"],
        isPercentage: true
      },
      {
        name: "总计整改",
        type: "bar",
        data: [7, 11, 2, 2, 6, 11, 18, 24, 7, 88],
        color: ["rgba(30, 118, 159, 1)", "rgba(30, 118, 159, 0.3)"],
        isPercentage: false
      }
    ]
  }
];

// 状态管理
const currentIndex = ref(0);
const chartRef = ref<InstanceType<typeof BarLineComboChart> | null>(null);
const chartContentRef = ref<HTMLElement | null>(null);
let autoplayTimer: number | null = null;
const isUserInteracting = ref(false);

// 更新图表尺寸
const updateChartSize = () => {
  if (!chartContentRef.value) return;

  requestAnimationFrame(() => {
    if (chartRef.value?.resize) {
      chartRef.value.resize();
    }
  });
};

// 切换方法
const changeIndex = (step: number) => {
  const newIndex = (currentIndex.value + step + params.length) % params.length;
  currentIndex.value = newIndex;

  nextTick(() => {
    setTimeout(() => {
      updateChartSize();
    }, 300);
  });
};

// 按钮点击处理
const handlePrev = () => {
  stopAutoplay();
  changeIndex(-1);
};

const handleNext = () => {
  stopAutoplay();
  changeIndex(1);
};

// 自动播放控制
const startAutoplay = () => {
  if (isUserInteracting.value) return;

  if (autoplayTimer) {
    clearInterval(autoplayTimer);
  }

  autoplayTimer = window.setInterval(() => {
    if (!document.hidden && !isUserInteracting.value) {
      changeIndex(1);
    }
  }, 4000);
};

const stopAutoplay = () => {
  isUserInteracting.value = true;
  if (autoplayTimer) {
    clearInterval(autoplayTimer);
    autoplayTimer = null;
  }
};

// 恢复自动播放
const resumeAutoplay = () => {
  isUserInteracting.value = false;
  startAutoplay();
};

// 点击事件
const handleChartClick = () => {
  stopAutoplay();
  emit("clickEvent", params[currentIndex.value].title);
};

// 页面可见性处理函数
const handleVisibilityChange = () => {
  if (document.hidden) {
    stopAutoplay();
  } else {
    resumeAutoplay();
  }
};

// 生命周期钩子
onMounted(() => {
  window.addEventListener("resize", updateChartSize);
  document.addEventListener("visibilitychange", handleVisibilityChange);

  nextTick(() => {
    setTimeout(() => {
      updateChartSize();
      startAutoplay();
    }, 300);
  });
});

onBeforeUnmount(() => {
  stopAutoplay();
  window.removeEventListener("resize", updateChartSize);
  document.removeEventListener("visibilitychange", handleVisibilityChange);
});

const emit = defineEmits<{
  (e: "clickEvent", title: string): void;
}>();
</script>

<style scoped lang="scss">
.chart-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .chart-content {
    position: relative;
    flex: 1;
    min-height: 0;
    padding: 10px;
    overflow: hidden;
    backdrop-filter: blur(4px);
    border: 1px solid rgb(18 142 255 / 10%);
    border-radius: 8px;
    .chart-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  }
  .chart-header {
    position: relative;
    display: flex;
    flex: none;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 0;
    margin-bottom: 10px;
    .nav-group {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 4px;
      background: linear-gradient(
        90deg,
        rgb(18 142 255 / 0%) 0%,
        rgb(18 142 255 / 10%) 25%,
        rgb(18 142 255 / 10%) 75%,
        rgb(18 142 255 / 0%) 100%
      );
      backdrop-filter: blur(4px);
      border-radius: 20px;
      .chart-title {
        position: relative;
        min-width: 200px;
        padding: 0 15px;
        font-size: 20px;
        font-weight: 500;
        color: #b1d8ff;
        text-align: center;
        cursor: pointer;
        transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        &::after {
          position: absolute;
          bottom: -2px;
          left: 50%;
          width: 0;
          height: 2px;
          content: "";
          background: linear-gradient(90deg, transparent, #00d5ff, transparent);
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateX(-50%);
        }
        &:hover {
          color: #00d5ff;
          text-shadow: 0 0 15px rgb(0 213 255 / 40%);
          &::after {
            width: 80%;
            opacity: 1;
          }
        }
      }
      .nav-btn {
        position: absolute;
        top: 50%;
        width: 28px;
        height: 28px;
        padding: 0;
        overflow: hidden;
        color: #b1d8ff;
        background: transparent;
        border: 1px solid rgb(18 142 255 / 30%);
        border-radius: 6px;
        transition: all 0.3s ease;
        transform: translateY(-50%);
        &.prev-btn {
          left: 0;
        }
        &.next-btn {
          right: 0;
        }
        .btn-bg {
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgb(18 142 255 / 10%) 0%, rgb(18 142 255 / 20%) 50%, rgb(18 142 255 / 10%) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        &:hover {
          color: #00d5ff;
          border-color: rgb(18 142 255 / 60%);
          box-shadow: 0 2px 8px rgb(0 213 255 / 20%);
          transform: translateY(calc(-50% - 1px));
          .btn-bg {
            opacity: 1;
          }
        }
        &:active {
          transform: translateY(-50%);
        }
        :deep(.el-icon) {
          z-index: 1;
          font-size: 16px;
        }
      }
    }
  }
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
