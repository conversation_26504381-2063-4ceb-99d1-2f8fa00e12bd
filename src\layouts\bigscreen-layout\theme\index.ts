import { computed } from 'vue';
import { useThemeStore } from '@/store/modules/theme';

/**
 * 大屏主题配置
 * 根据当前主题模式（亮色/暗色）自动切换大屏样式
 */
export function useBigscreenTheme() {
  const themeStore = useThemeStore();

  const bigscreenTheme = computed(() => {
    // 大屏统一使用深蓝色主题，不区分亮暗模式
    return {
      // 基础颜色配置 - 统一的大屏蓝色主题
      colors: {
        // 对话框背景色
        dialogBg: '#0a1929',
        // 边框颜色 - 亮蓝色边框
        borderColor: '#00d4ff',
        // 主要文字颜色 - 亮蓝白色
        textColor: '#ffffff',
        // 次要文字颜色 - 浅蓝色
        secondaryTextColor: '#7dd3fc',
        // 聊天区域背景
        chatBg: '#0f172a',
        // 用户消息背景
        userMsgBg: '#1e40af',
        // 系统消息背景
        systemMsgBg: '#1e3a8a',
        // 输入框背景
        inputBg: '#1e293b',
        // 按钮背景 - 科技蓝
        buttonBg: '#0ea5e9',
        // 设置项背景
        itemBg: '#0f172a',
        // 主背景色 - 深蓝黑渐变
        mainBg: '#020617',
        // 卡片背景色 - 半透明深蓝
        cardBg: 'rgba(15, 23, 42, 0.8)',
        // 成功色 - 青色
        successColor: '#06d6a0',
        // 警告色 - 橙色
        warningColor: '#fbbf24',
        // 错误色 - 红色
        errorColor: '#ef4444',
        // 信息色 - 蓝色
        infoColor: '#3b82f6'
      },

      // 阴影配置 - 蓝色发光效果
      shadows: {
        card: '0 4px 20px rgba(0, 212, 255, 0.15), 0 2px 8px rgba(0, 0, 0, 0.3)',
        dialog: '0 8px 32px rgba(0, 212, 255, 0.2), 0 4px 16px rgba(0, 0, 0, 0.4)',
        button: '0 2px 12px rgba(14, 165, 233, 0.4), 0 1px 4px rgba(0, 0, 0, 0.2)'
      },

      // 渐变配置 - 科技感蓝色渐变
      gradients: {
        primary: 'linear-gradient(135deg, #0ea5e9 0%, #1e40af 100%)',
        background: 'linear-gradient(135deg, #020617 0%, #0f172a 50%, #1e293b 100%)',
        card: 'linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.7) 100%)'
      }
    };
  });

  return {
    bigscreenTheme,
    isDark: computed(() => themeStore.darkMode),
    themeStore
  };
}

/**
 * 大屏主题CSS变量
 * 用于在CSS中直接使用主题变量
 */
export function getBigscreenCssVars(theme: ReturnType<typeof useBigscreenTheme>['bigscreenTheme']['value']) {
  return {
    '--bigscreen-dialog-bg': theme.colors.dialogBg,
    '--bigscreen-border-color': theme.colors.borderColor,
    '--bigscreen-text-color': theme.colors.textColor,
    '--bigscreen-secondary-text-color': theme.colors.secondaryTextColor,
    '--bigscreen-chat-bg': theme.colors.chatBg,
    '--bigscreen-user-msg-bg': theme.colors.userMsgBg,
    '--bigscreen-system-msg-bg': theme.colors.systemMsgBg,
    '--bigscreen-input-bg': theme.colors.inputBg,
    '--bigscreen-button-bg': theme.colors.buttonBg,
    '--bigscreen-item-bg': theme.colors.itemBg,
    '--bigscreen-main-bg': theme.colors.mainBg,
    '--bigscreen-card-bg': theme.colors.cardBg,
    '--bigscreen-success-color': theme.colors.successColor,
    '--bigscreen-warning-color': theme.colors.warningColor,
    '--bigscreen-error-color': theme.colors.errorColor,
    '--bigscreen-info-color': theme.colors.infoColor,
    '--bigscreen-card-shadow': theme.shadows.card,
    '--bigscreen-dialog-shadow': theme.shadows.dialog,
    '--bigscreen-button-shadow': theme.shadows.button,
    '--bigscreen-primary-gradient': theme.gradients.primary,
    '--bigscreen-background-gradient': theme.gradients.background,
    '--bigscreen-card-gradient': theme.gradients.card
  };
}

/**
 * 大屏主题预设
 */
export const bigscreenThemePresets = {
  // 科技蓝主题（默认）
  tech: {
    light: {
      primary: '#1765c6',
      secondary: '#0a3b7d',
      background: '#041426',
      surface: '#051a3e'
    },
    dark: {
      primary: '#1565c0',
      secondary: '#0d47a1',
      background: '#0a0e1a',
      surface: '#0d1b2a'
    }
  },

  // 绿色主题
  green: {
    light: {
      primary: '#1e9b4c',
      secondary: '#0d5d2a',
      background: '#0a1f0f',
      surface: '#0f2d17'
    },
    dark: {
      primary: '#4caf50',
      secondary: '#2e7d32',
      background: '#0a1f0f',
      surface: '#1b5e20'
    }
  },

  // 紫色主题
  purple: {
    light: {
      primary: '#7b1fa2',
      secondary: '#4a148c',
      background: '#1a0a2e',
      surface: '#2d1b4e'
    },
    dark: {
      primary: '#9c27b0',
      secondary: '#6a1b9a',
      background: '#1a0a2e',
      surface: '#4a148c'
    }
  }
};
