import { computed } from 'vue';
import { useThemeStore } from '@/store/modules/theme';

/**
 * 大屏主题配置
 * 根据当前主题模式（亮色/暗色）自动切换大屏样式
 */
export function useBigscreenTheme() {
  const themeStore = useThemeStore();

  const bigscreenTheme = computed(() => {
    const isDark = themeStore.darkMode;

    return {
      // 基础颜色配置
      colors: {
        // 对话框背景色
        dialogBg: isDark ? '#0a1929' : '#051a3e',
        // 边框颜色
        borderColor: isDark ? '#1e3a8a' : '#1765c6',
        // 主要文字颜色
        textColor: isDark ? '#e3f2fd' : '#b1d8ff',
        // 次要文字颜色
        secondaryTextColor: isDark ? '#90caf9' : '#8ab3e0',
        // 聊天区域背景
        chatBg: isDark ? '#0d1b2a' : '#051a3e',
        // 用户消息背景
        userMsgBg: isDark ? '#1565c0' : '#1765c6',
        // 系统消息背景
        systemMsgBg: isDark ? '#0d47a1' : '#0a3b7d',
        // 输入框背景
        inputBg: isDark ? '#0d47a1' : '#0a3b7d',
        // 按钮背景
        buttonBg: isDark ? '#1565c0' : '#1765c6',
        // 设置项背景
        itemBg: isDark ? '#0d1b2a' : '#051a3e',
        // 主背景色
        mainBg: isDark ? '#0a0e1a' : '#041426',
        // 卡片背景色
        cardBg: isDark ? '#0f1419' : '#051a3e',
        // 成功色
        successColor: isDark ? '#4caf50' : '#1e9b4c',
        // 警告色
        warningColor: isDark ? '#ff9800' : '#f57c00',
        // 错误色
        errorColor: isDark ? '#f44336' : '#9b1e1e',
        // 信息色
        infoColor: isDark ? '#2196f3' : '#1976d2'
      },

      // 阴影配置
      shadows: {
        card: isDark ? '0 4px 12px rgba(0, 0, 0, 0.3)' : '0 4px 12px rgba(5, 26, 62, 0.25)',
        dialog: isDark ? '0 8px 24px rgba(0, 0, 0, 0.4)' : '0 8px 24px rgba(5, 26, 62, 0.3)',
        button: isDark ? '0 2px 8px rgba(21, 101, 192, 0.3)' : '0 2px 8px rgba(23, 101, 198, 0.25)'
      },

      // 渐变配置
      gradients: {
        primary: isDark
          ? 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)'
          : 'linear-gradient(135deg, #1765c6 0%, #0a3b7d 100%)',
        background: isDark
          ? 'linear-gradient(135deg, #0a0e1a 0%, #0d1b2a 100%)'
          : 'linear-gradient(135deg, #041426 0%, #051a3e 100%)',
        card: isDark
          ? 'linear-gradient(135deg, #0f1419 0%, #0d1b2a 100%)'
          : 'linear-gradient(135deg, #051a3e 0%, #041426 100%)'
      }
    };
  });

  return {
    bigscreenTheme,
    isDark: computed(() => themeStore.darkMode),
    themeStore
  };
}

/**
 * 大屏主题CSS变量
 * 用于在CSS中直接使用主题变量
 */
export function getBigscreenCssVars(theme: ReturnType<typeof useBigscreenTheme>['bigscreenTheme']['value']) {
  return {
    '--bigscreen-dialog-bg': theme.colors.dialogBg,
    '--bigscreen-border-color': theme.colors.borderColor,
    '--bigscreen-text-color': theme.colors.textColor,
    '--bigscreen-secondary-text-color': theme.colors.secondaryTextColor,
    '--bigscreen-chat-bg': theme.colors.chatBg,
    '--bigscreen-user-msg-bg': theme.colors.userMsgBg,
    '--bigscreen-system-msg-bg': theme.colors.systemMsgBg,
    '--bigscreen-input-bg': theme.colors.inputBg,
    '--bigscreen-button-bg': theme.colors.buttonBg,
    '--bigscreen-item-bg': theme.colors.itemBg,
    '--bigscreen-main-bg': theme.colors.mainBg,
    '--bigscreen-card-bg': theme.colors.cardBg,
    '--bigscreen-success-color': theme.colors.successColor,
    '--bigscreen-warning-color': theme.colors.warningColor,
    '--bigscreen-error-color': theme.colors.errorColor,
    '--bigscreen-info-color': theme.colors.infoColor,
    '--bigscreen-card-shadow': theme.shadows.card,
    '--bigscreen-dialog-shadow': theme.shadows.dialog,
    '--bigscreen-button-shadow': theme.shadows.button,
    '--bigscreen-primary-gradient': theme.gradients.primary,
    '--bigscreen-background-gradient': theme.gradients.background,
    '--bigscreen-card-gradient': theme.gradients.card
  };
}

/**
 * 大屏主题预设
 */
export const bigscreenThemePresets = {
  // 科技蓝主题（默认）
  tech: {
    light: {
      primary: '#1765c6',
      secondary: '#0a3b7d',
      background: '#041426',
      surface: '#051a3e'
    },
    dark: {
      primary: '#1565c0',
      secondary: '#0d47a1',
      background: '#0a0e1a',
      surface: '#0d1b2a'
    }
  },

  // 绿色主题
  green: {
    light: {
      primary: '#1e9b4c',
      secondary: '#0d5d2a',
      background: '#0a1f0f',
      surface: '#0f2d17'
    },
    dark: {
      primary: '#4caf50',
      secondary: '#2e7d32',
      background: '#0a1f0f',
      surface: '#1b5e20'
    }
  },

  // 紫色主题
  purple: {
    light: {
      primary: '#7b1fa2',
      secondary: '#4a148c',
      background: '#1a0a2e',
      surface: '#2d1b4e'
    },
    dark: {
      primary: '#9c27b0',
      secondary: '#6a1b9a',
      background: '#1a0a2e',
      surface: '#4a148c'
    }
  }
};
