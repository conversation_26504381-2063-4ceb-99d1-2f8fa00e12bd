<script setup lang="ts">
import { ref } from 'vue';
import { useBigscreenTheme } from '@/layouts/bigscreen-layout/theme/index';
import AiDialog from '@/layouts/bigscreen-layout/components/AiDialog/index.vue';
import SettingsDialog from '@/layouts/bigscreen-layout/components/SettingsDialog/index.vue';

defineOptions({
  name: 'BigscreenDemo'
});

const { bigscreenTheme, themeStore } = useBigscreenTheme();

// 对话框状态
const aiDialogVisible = ref(false);
const settingsDialogVisible = ref(false);

// 示例数据
const chartData = ref([
  { name: '销售额', value: 12580, trend: '+12.5%', color: 'success' },
  { name: '订单数', value: 3420, trend: '+8.2%', color: 'info' },
  { name: '用户数', value: 8960, trend: '+15.3%', color: 'warning' },
  { name: '转化率', value: 68.5, trend: '-2.1%', color: 'error' }
]);

const openAiDialog = () => {
  aiDialogVisible.value = true;
};

const openSettingsDialog = () => {
  settingsDialogVisible.value = true;
};
</script>

<template>
  <div class="bigscreen-demo h-full w-full p-6">
    <!-- 顶部操作栏 -->
    <div class="mb-6 flex items-center justify-between">
      <h1 class="text-2xl font-bold" :style="{ color: bigscreenTheme.colors.textColor }">
        大屏数据展示
      </h1>
      <div class="flex gap-4">
        <NButton 
          type="primary" 
          class="bigscreen-btn"
          :style="{ 
            backgroundColor: bigscreenTheme.colors.buttonBg, 
            borderColor: bigscreenTheme.colors.borderColor,
            color: bigscreenTheme.colors.textColor
          }"
          @click="openAiDialog"
        >
          <template #icon>
            <NIcon>
              <SvgIcon icon="mdi:robot" />
            </NIcon>
          </template>
          AI助手
        </NButton>
        <NButton 
          type="primary" 
          class="bigscreen-btn"
          :style="{ 
            backgroundColor: bigscreenTheme.colors.buttonBg, 
            borderColor: bigscreenTheme.colors.borderColor,
            color: bigscreenTheme.colors.textColor
          }"
          @click="openSettingsDialog"
        >
          <template #icon>
            <NIcon>
              <SvgIcon icon="mdi:cog" />
            </NIcon>
          </template>
          设置
        </NButton>
        <NButton 
          type="primary" 
          class="bigscreen-btn"
          :style="{ 
            backgroundColor: bigscreenTheme.colors.successColor, 
            borderColor: bigscreenTheme.colors.borderColor,
            color: bigscreenTheme.colors.textColor
          }"
          @click="themeStore.toggleThemeScheme"
        >
          <template #icon>
            <NIcon>
              <SvgIcon :icon="themeStore.darkMode ? 'mdi:weather-sunny' : 'mdi:weather-night'" />
            </NIcon>
          </template>
          {{ themeStore.darkMode ? '亮色' : '暗色' }}
        </NButton>
      </div>
    </div>

    <!-- 数据卡片网格 -->
    <div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
      <div 
        v-for="item in chartData" 
        :key="item.name"
        class="bigscreen-card rounded-lg p-6"
        :style="{ 
          backgroundColor: bigscreenTheme.colors.cardBg,
          border: `1px solid ${bigscreenTheme.colors.borderColor}`,
          boxShadow: bigscreenTheme.shadows.card
        }"
      >
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium" :style="{ color: bigscreenTheme.colors.textColor }">
              {{ item.name }}
            </h3>
            <p class="mt-2 text-3xl font-bold" :style="{ color: bigscreenTheme.colors.textColor }">
              {{ item.value }}{{ item.name === '转化率' ? '%' : '' }}
            </p>
            <p 
              class="mt-1 text-sm"
              :style="{ 
                color: item.trend.startsWith('+') 
                  ? bigscreenTheme.colors.successColor 
                  : bigscreenTheme.colors.errorColor 
              }"
            >
              {{ item.trend }}
            </p>
          </div>
          <div 
            class="h-12 w-12 rounded-full flex items-center justify-center"
            :style="{ backgroundColor: bigscreenTheme.colors[item.color + 'Color'] }"
          >
            <NIcon size="24" :style="{ color: bigscreenTheme.colors.textColor }">
              <SvgIcon 
                :icon="
                  item.name === '销售额' ? 'mdi:currency-usd' :
                  item.name === '订单数' ? 'mdi:cart' :
                  item.name === '用户数' ? 'mdi:account-group' :
                  'mdi:chart-line'
                " 
              />
            </NIcon>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
      <div 
        class="bigscreen-card rounded-lg p-6"
        :style="{ 
          backgroundColor: bigscreenTheme.colors.cardBg,
          border: `1px solid ${bigscreenTheme.colors.borderColor}`,
          boxShadow: bigscreenTheme.shadows.card
        }"
      >
        <h3 class="mb-4 text-xl font-semibold" :style="{ color: bigscreenTheme.colors.textColor }">
          销售趋势
        </h3>
        <div 
          class="h-64 flex items-center justify-center rounded-lg"
          :style="{ backgroundColor: bigscreenTheme.colors.itemBg }"
        >
          <p :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
            图表区域 - 这里可以放置 ECharts 或其他图表组件
          </p>
        </div>
      </div>

      <div 
        class="bigscreen-card rounded-lg p-6"
        :style="{ 
          backgroundColor: bigscreenTheme.colors.cardBg,
          border: `1px solid ${bigscreenTheme.colors.borderColor}`,
          boxShadow: bigscreenTheme.shadows.card
        }"
      >
        <h3 class="mb-4 text-xl font-semibold" :style="{ color: bigscreenTheme.colors.textColor }">
          用户分布
        </h3>
        <div 
          class="h-64 flex items-center justify-center rounded-lg"
          :style="{ backgroundColor: bigscreenTheme.colors.itemBg }"
        >
          <p :style="{ color: bigscreenTheme.colors.secondaryTextColor }">
            图表区域 - 这里可以放置饼图或地图组件
          </p>
        </div>
      </div>
    </div>

    <!-- 对话框组件 -->
    <AiDialog v-model:visible="aiDialogVisible" />
    <SettingsDialog v-model:visible="settingsDialogVisible" />
  </div>
</template>

<style scoped>
.bigscreen-demo {
  background: v-bind('bigscreenTheme.gradients.background');
  min-height: 100vh;
}

.bigscreen-card {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.bigscreen-card:hover {
  transform: translateY(-2px);
  box-shadow: v-bind('bigscreenTheme.shadows.dialog');
}

.bigscreen-btn {
  transition: all 0.3s ease;
  box-shadow: v-bind('bigscreenTheme.shadows.button');
}

.bigscreen-btn:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}
</style>
