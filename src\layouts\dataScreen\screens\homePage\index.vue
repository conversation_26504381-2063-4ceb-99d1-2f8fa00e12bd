<template>
  <div class="dataScreen-lf">
    <div class="dataScreen-top">
      <NormalCard title="网络运行情况">
        <LeftTop @click-event="handleDialogOpen" />
      </NormalCard>
    </div>
    <div class="dataScreen-center">
      <NormalCard title="倒三角支撑">
        <template #button>
          <div class="button-group">
            <el-button
              type="primary"
              size="small"
              class="btnChange"
              :class="{ activeBtn: activeTab === '年' }"
              @click="handleTabChange('年')"
            >
              年
            </el-button>
            <el-button
              type="primary"
              size="small"
              class="btnChange"
              :class="{ activeBtn: activeTab === '月' }"
              @click="handleTabChange('月')"
            >
              月
            </el-button>
          </div>
        </template>
        <LeftCenter :active-tab="activeTab" @click-event="handleDialogOpen" />
      </NormalCard>
    </div>
    <div class="dataScreen-bottom">
      <NormalCard title="易访库用户跟进">
        <LeftBottom @click-event="handleDialogOpen" />
      </NormalCard>
    </div>
  </div>
  <div class="dataScreen-ct">
    <div class="dataScreen-map">
      <div class="dataScreen-map-title">湖南长沙</div>
      <div class="dataScreen-alarm">
        <ChinaMapChart />
      </div>
    </div>
    <div class="dataScreen-cb">
      <NormalCard title="满意度">
        <CenterBottom @click-event="handleDialogOpen" />
      </NormalCard>
    </div>
  </div>
  <div class="dataScreen-rg">
    <div class="dataScreen-top">
      <NormalCard title="省重点工作">
        <RightTop @click-event="handleDialogOpen" />
      </NormalCard>
    </div>
    <div class="dataScreen-center">
      <NormalCard title="网络故障调度">
        <RightCenter @click-event="handleDialogOpen" />
      </NormalCard>
    </div>
    <div class="dataScreen-bottom">
      <NormalCard title="应急通信调度">
        <template #button>
          <el-button type="primary" size="small" class="btnChange" @click="() => handleDialogOpen('演唱会', 'emergency')">
            演唱会
          </el-button>
        </template>
        <RightBottom @click-event="handleDialogOpen" />
      </NormalCard>
    </div>
  </div>

  <el-dialog
    v-model="visible"
    :title="topTitle"
    :width="1350"
    :close-on-click-modal="true"
    :centered="true"
    class="dt-dialog"
    modal-class="dt-overlay"
    destroy-on-close
    append-to-body
  >
    <el-row :gutter="16">
      <el-col :span="24">
        <Dialog />
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";

// 组件导入
import { NormalCard, ChinaMapChart } from "../../components";
import { LeftTop, LeftCenter, LeftBottom, RightTop, RightCenter, RightBottom, CenterBottom, Dialog } from "./components";

/**
 * 响应式变量定义
 */
// interface DialogState {
//   visible: boolean;
//   title: string;
// }

// 对话框状态
const visible = ref<boolean>(false);
const topTitle = ref<string>("");

// 当前激活的标签页
const activeTab = ref<string>("月");

/**
 * 打开指标对话框
 * @param {string} title - 对话框标题
 * @param {string} [flag] - 可选的标记参数，用于特殊处理逻辑
 */
const handleDialogOpen = (title: string, flag?: string): void => {
  topTitle.value = title;
  visible.value = true;
  console.log("Dialog opened:", title, flag);
};

/**
 * 切换标签页
 * @param {string} tab - 标签页名称
 */
const handleTabChange = (tab: string): void => {
  activeTab.value = tab;
};
</script>

<style lang="scss" scoped></style>
