<template>
  <div class="dataScreen-header">
    <div class="left" style="text-align: left">
      <img src="../assets/images/logoIcon.png" class="logoImg" alt="" />
      <el-link @click="goTo('homePage')">首页</el-link>
      <el-divider direction="vertical" />
      <el-link @click="goTo('homePage')">关注</el-link>
      <el-divider direction="vertical" />
      <el-link @click="goTo('homePage')">推荐</el-link>
      <el-divider direction="vertical" />
      <el-link @click="goTo('homePage')">热门</el-link>
      <el-divider direction="vertical" />
      <el-link>本地</el-link>
    </div>
    <div class="logo">{{ title }}</div>
    <div class="right">
      <el-link class="p-0" @click="openAiWindow" :underline="false">
        <img src="../assets/images/robot.png" alt="" />
      </el-link>
      <el-divider direction="vertical" />
      <el-link :underline="false" @click="openInNewTab('http://10.154.40.42:8090/home')">
        <IconifyIcon class="iconfont" icon="mdi:calendar-clock" />
        <!-- <i class="iconfont icon-tiaodutai"></i> -->
        调度台
      </el-link>
      <el-divider direction="vertical" />
      <!-- 假设 "我的工作台" 链接与调度台相同 -->
      <el-link :underline="false" @click="goTo('home')">
        <IconifyIcon class="iconfont" icon="ion:desktop-outline" />
        <!-- <i class="iconfont icon-gongzuotai"></i> -->
        我的工作台
      </el-link>
      <el-divider direction="vertical" />
      <el-link class="pl-8 pr-8" :underline="false"><i class="iconfont icon-xiaoxi mb0"></i></el-link>
      <el-divider direction="vertical" />
      <el-link class="pl-8 pr-8" :underline="false"><Fullscreen class="iconfont mb0" /></el-link>
    </div>
  </div>
  <el-dialog
    class="dt-dialog"
    v-model="aiVisible"
    title="ChatGpt"
    :footer="null"
    :width="500"
    centered
    :closable="false"
    :body-style="{ padding: '0px' }"
    mask-closable
  >
    <div style="display: flex; align-items: center; justify-content: center; height: 700px">
      <iframe allow="microphone;" src="https://chat18.aichatos8.com/#/" style="width: 100%; height: 100%"></iframe>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, withDefaults, defineProps } from "vue";
import { useRouter } from "vue-router";
import Fullscreen from "@/layouts/components/Header/components/Fullscreen.vue";

/**
 * @typedef {Object} Props
 * @property {string} [title] - 顶部标题
 */

/**
 * @type {Props}
 */
interface Props {
  title?: string;
}

withDefaults(defineProps<Props>(), { title: "网络中台" });
const router = useRouter();
const aiVisible = ref(false);

/**
 * 路由跳转函数
 * @param {string} url - 目标路由名称
 */
const goTo = (url: string) => {
  if (router.currentRoute.value.path !== url) {
    router.push({ name: url });
  }
};

/**
 * 在新标签页打开链接
 * @param {string} url - 目标URL
 */
const openInNewTab = (url: string) => {
  window.open(url, "_blank");
};

/**
 * 打开AI对话框
 */
const openAiWindow = () => {
  aiVisible.value = true;
};
</script>

<style lang="scss" scoped>
@import "../assets/style/index";
</style>
