export const svgData = middleText => {
  return (
    `
<svg
          width="204px"
          height="145px"
          viewBox="0 0 204 145"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <defs>
            <linearGradient x1="12.8688574%" y1="25.7318412%" x2="110.837913%" y2="64.5888662%" id="linearGradient-1">
              <stop stop-color="#66E2FB" offset="0%"></stop>
              <stop stop-color="#66E2FB" stop-opacity="0" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="110.198092%" y1="25.4747119%" x2="13.508682%" y2="64.845996%" id="linearGradient-2">
              <stop stop-color="#66E2FB" offset="0%"></stop>
              <stop stop-color="#66E2FB" stop-opacity="0" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="13.508682%" y1="64.845996%" x2="110.198092%" y2="25.4747119%" id="linearGradient-3">
              <stop stop-color="#66E2FB" offset="0%"></stop>
              <stop stop-color="#66E2FB" stop-opacity="0" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="110.837913%" y1="64.5888662%" x2="12.8688574%" y2="25.7318412%" id="linearGradient-4">
              <stop stop-color="#66E2FB" offset="0%"></stop>
              <stop stop-color="#66E2FB" stop-opacity="0" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="117.376506%" y1="67.3765123%" x2="-17.3765123%" y2="67.3765123%" id="linearGradient-5">
              <stop stop-color="#00FF92" stop-opacity="0.498442978" offset="0%"></stop>
              <stop stop-color="#128EFF" stop-opacity="0.501502395" offset="100%"></stop>
            </linearGradient>
            <text id="text-6" font-family="YouSheBiaoTiHei" font-size="20" font-weight="normal" line-spacing="28">
              <tspan x="-1" y="20">` +
    middleText +
    `</tspan>
            </text>
            <filter x="-15.4%" y="-23.1%" width="130.8%" height="146.2%" filterUnits="objectBoundingBox" id="filter-7">
              <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
              <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
              <feColorMatrix
                values="0 0 0 0 0   0 0 0 0 0.400000006   0 0 0 0 1  0 0 0 1 0"
                type="matrix"
                in="shadowBlurOuter1"
              ></feColorMatrix>
            </filter>
          </defs>
          <g id="资源看板-已修改" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="资源看板2.0" transform="translate(-148, -243.2727)" fill-rule="nonzero">
              <g id="zuo" transform="translate(20, 114)">
                <g id="编组-14" transform="translate(16, 55)">
                  <g id="Row" transform="translate(0, 30)">
                    <g id="Card" transform="translate(114, 45)">
                      <g>
                        <g id="Card-BG" transform="translate(27.9412, 0)">
                          <rect x="0" y="0" width="144.117645" height="144"></rect>
                        </g>
                        <g id="Card-Body" transform="translate(0, 8.8163)">
                          <g id="LineGroup" opacity="0.296316952" transform="translate(0, 0)" stroke-width="4">
                            <g id="Line" stroke="#B1D8FF" stroke-opacity="0.0799999982">
                              <line x1="0" y1="0" x2="50" y2="31.5918369" id="Path"></line>
                              <line x1="200" y1="0" x2="150" y2="31.5918369" id="Path"></line>
                              <line x1="0" y1="126.36735" x2="50" y2="94.7755127" id="Path"></line>
                              <line x1="200" y1="126.36735" x2="150" y2="94.7755127" id="Path"></line>
                            </g>
                            <g id="Line" transform="translate(12.5, 7.3469)" stroke-linecap="round">
                              <line x1="0" y1="0" x2="12.5" y2="7.92419815" id="Path" stroke="url(#linearGradient-1)"></line>
                              <line x1="175" y1="0" x2="162.5" y2="7.92419815" id="Path" stroke="url(#linearGradient-2)"></line>
                              <line
                                x1="0"
                                y1="110.938778"
                                x2="12.5"
                                y2="103.01458"
                                id="Path"
                                stroke="url(#linearGradient-3)"
                              ></line>
                              <line
                                x1="175"
                                y1="110.938778"
                                x2="162.5"
                                y2="103.01458"
                                id="Path"
                                stroke="url(#linearGradient-4)"
                              ></line>
                            </g>
                          </g>
                          <g id="Point-BG-B" transform="translate(10.2941, 11.1837)">
                            <circle
                              id="Rectangle"
                              stroke-opacity="0.29848668"
                              stroke="#00D5FF"
                              stroke-width="2"
                              fill-opacity="0.118362106"
                              fill="#128EFF"
                              stroke-linecap="round"
                              cx="89"
                              cy="52"
                              r="52"
                            ></circle>
                            <path
                              d="M25.8324423,25.5653534 C22.4532799,33.7078943 20.5882359,42.6363239 20.5882359,52 C20.5882359,61.363678 22.4532799,70.2921066 25.8324423,78.4346466"
                              id="Rectangle"
                              stroke="#26A7FF"
                              stroke-width="3"
                              stroke-linecap="round"
                            ></path>
                            <path
                              d="M153.57933,78.4346466 C156.958493,70.2921066 158.823537,61.363678 158.823537,52 C158.823537,42.6363239 156.958493,33.7078943 153.57933,25.5653534"
                              id="Path"
                              stroke="#00FF92"
                              stroke-width="3"
                              stroke-linecap="round"
                            ></path>
                            <line
                              x1="0"
                              y1="51.9999992"
                              x2="13.6029415"
                              y2="51.9999992"
                              id="Line"
                              stroke-opacity="0.0799999982"
                              stroke="#B1D8FF"
                              stroke-width="2"
                            ></line>
                          </g>
                        </g>
                        <circle id="Card-BG" fill="url(#linearGradient-5)" cx="99" cy="72" r="35"></circle>
                      </g>
                      <g id="人员" transform="translate(82, 59)" fill="#FFFFFF">
                        <use fill-opacity="1" filter="url(#filter-7)" xlink:href="#text-6"></use>
                        <use xlink:href="#text-6"></use>
                        <use fill-opacity="1" xlink:href="#text-6"></use>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </svg>
`
  );
};
