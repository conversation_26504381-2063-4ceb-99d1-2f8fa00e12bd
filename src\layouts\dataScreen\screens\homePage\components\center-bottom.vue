<template>
  <div class="chart-container">
    <div class="chart-grid">
      <div v-for="(item, index) in chartData" :key="index" class="chart-item" :class="`item-${index + 1}`">
        <div class="item-header">
          <span class="item-title">{{ item.title }}</span>
          <div class="item-indicator"></div>
        </div>
        <div class="item-content">
          <BarChart :data="formatChartData(item)" :color="getChartColors(index)" :show-y-axis="false" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BarChart from "@/components/ECharts/charts/BarChartBak.vue";

interface ChartItem {
  title: string;
  category: string[];
  barData: number[][];
}

// 图表数据
const chartData: ChartItem[] = [
  {
    title: "移动网络质量",
    category: ["移动", "电信"],
    barData: [[78.64, 78.73]]
  },
  {
    title: "家宽上网质量",
    category: ["移动", "电信"],
    barData: [[73.99, 74.48]]
  },
  {
    title: "家宽装维服务",
    category: ["移动", "电信"],
    barData: [[85.96, 88.48]]
  },
  {
    title: "政企产品质量",
    category: ["专线", "企宽", "物联网"],
    barData: [[98.67, 98.81, 0]]
  }
];

// 格式化图表数据，适配新的 BarChart 组件
const formatChartData = (item: ChartItem) => {
  return {
    xAxis: item.category,
    series: item.barData[0]
  };
};

// 获取图表颜色
const getChartColors = (index: number): string[] => {
  const colors = [
    ["rgba(89,211,255,1)", "rgba(89,211,255,0.3)"],
    ["rgba(46,184,255,1)", "rgba(46,184,255,0.3)"],
    ["rgba(86,146,255,1)", "rgba(86,146,255,0.3)"]
  ];
  return colors[index] || colors[0];
};
</script>

<style scoped lang="scss">
.chart-container {
  position: relative;
  width: 100%;
  height: calc(100% - 5px);
  overflow: hidden;
  .chart-grid {
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    width: 100%;
    height: 100%;
    .chart-item {
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;
      min-height: 0;
      padding: 1px; // 添加内边距为发光效果预留空间
      background: transparent; // 背景改为透明
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      // 内容容器
      &-inner {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        background: linear-gradient(180deg, rgb(18 142 255 / 8%), rgb(18 142 255 / 2%));
        backdrop-filter: blur(4px);
        border: 1px solid rgb(18 142 255 / 10%);
        border-radius: 2px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      // 发光边框效果
      &::before {
        position: absolute;
        inset: 0;
        padding: 1px;
        pointer-events: none;
        content: "";
        background: linear-gradient(180deg, rgb(255 255 255 / 10%), rgb(255 255 255 / 3%));
        border-radius: 2px;
        opacity: 0;
        mask:
          linear-gradient(#ffffff 0 0) content-box,
          linear-gradient(#ffffff 0 0);
        mask-composite: exclude;
        transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      // 顶部光效
      &::after {
        position: absolute;
        top: 1px; // 调整位置以适应新的内边距
        left: 50%;
        width: 80%;
        height: 1px;
        content: "";
        background: linear-gradient(90deg, rgb(255 255 255 / 0%) 0%, rgb(255 255 255 / 20%) 50%, rgb(255 255 255 / 0%) 100%);
        opacity: 0;
        transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(-50%);
      }
      &:hover {
        transform: translateY(-1px);
        .item-inner {
          border-color: rgb(18 142 255 / 30%);
          box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
        }
        &::before {
          opacity: 1;
        }
        &::after {
          opacity: 1;
        }
        .item-header {
          .item-title {
            color: #ffffff;
            text-shadow: 0 0 8px rgb(255 255 255 / 30%);
          }
          .item-indicator {
            filter: brightness(1.2);
            transform: scale(1.1);
          }
        }
      }
      .item-header {
        display: flex;
        flex: none;
        align-items: center;
        justify-content: space-between;
        height: 30px;
        padding: 0 8px;
        border-bottom: 1px solid rgb(18 142 255 / 10%);
        .item-title {
          overflow: hidden;
          font-size: 20px;
          font-weight: 600;
          color: #ffffff;
          text-overflow: ellipsis;
          white-space: nowrap;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .item-indicator {
          flex-shrink: 0;
          width: 10px;
          height: 10px;
          margin-left: 4px;
          background: #00d5ff;
          border-radius: 50%;
          box-shadow: 0 0 4px rgb(0 213 255 / 50%);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          animation: pulse 2s infinite;
        }
      }
      .item-content {
        flex: 1;
        min-height: 0;
        padding: 2px;
        overflow: hidden;
      }

      // 为不同的指示器设置颜色
      &.item-1 .item-indicator {
        background: #00d5ff;
        box-shadow: 0 0 4px rgb(0 213 255 / 50%);
      }
      &.item-2 .item-indicator {
        background: #00ffb3;
        box-shadow: 0 0 4px rgb(0 255 179 / 50%);
      }
      &.item-3 .item-indicator {
        background: #ffb300;
        box-shadow: 0 0 4px rgb(255 179 0 / 50%);
      }
      &.item-4 .item-indicator {
        background: #ff61d8;
        box-shadow: 0 0 4px rgb(255 97 216 / 50%);
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
