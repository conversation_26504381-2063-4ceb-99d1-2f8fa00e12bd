/**
 * 表格列属性接口
 */
export interface ColumnProps {
  /** 字段名 */
  prop: string;
  /** 列标题 */
  label: string;
  /** 列宽度 */
  width?: string;
  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean;
  /** 列对齐方式 */
  align?: "left" | "center" | "right";
  /** 自定义列样式 */
  customStyle?: Record<string, string>;
  /** 自定义插槽名称 */
  slotName?: string;
}

/**
 * 表格合计方法参数接口
 */
export interface SummaryMethodProps {
  columns: ColumnProps[];
  data: any[];
}

/**
 * 表格单元格类名方法参数接口
 */
export interface CellClassNameProps {
  column: ColumnProps;
  row: any;
}

/**
 * 表格实例接口
 */
export interface DataScreenTableInstance {
  /** 开始滚动 */
  startScroll: () => void;
  /** 停止滚动 */
  stopScroll: () => void;
}
